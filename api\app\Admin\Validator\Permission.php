<?php

namespace App\Admin\Validator;

use Illuminate\Validation\Rule;
use App\Admin\Models;

class Permission extends \App\Api\Util\CustomerValidator
{
    public function __construct()
    {
        $this->rules = [
            'id' => ['integer'],
            'name' => ['string', 'max:255'],
            'title' => ['string', 'max:255'],
            'type' => [
                Rule::in([
                    Models\Permissions::TYPE_MENU,
                    Models\Permissions::TYPE_PAGE,
                    Models\Permissions::TYPE_ITEM,
                ]),
            ],
            'parent_id' => ['integer'],
            'component_path' => ['string', 'max:255', 'regex:/^(?:(?:\/[\w\-]+)+|LAYOUT)$/'],
            'order' => ['integer'],
            'icon' => ['string', 'max:255'],
            'is_iframe' => ['boolean'],
            'frame_src' => ['max:255', 'url'],
            'is_hidden' => ['boolean'],
            'route_path' => ['string', 'max:255', 'regex:/^[\w\-\/:]+$/'],
            'api' => ['max:255', 'regex:/^(?:\/[\w\-]+)+$/'],
            'no_cache' => ['boolean'],
            'status' => [
                Rule::in([
                    Models\Permissions::STATUS_DISABLE,
                    Models\Permissions::STATUS_ENABLE,
                ]),
            ],
        ];
    }

    public function cookRulesBeforeCheck($data)
    {
        if (isset($data['type'])) {
            switch ($data['type']) {
                case Models\Permissions::TYPE_MENU:
                    array_unshift($this->rules['icon'], 'present');
                    // array_unshift($this->rules['is_iframe'], 'present');
                    // if (($data['is_iframe']?? false)) {
                    //     array_unshift($this->rules['frame_src'], 'present');
                    // }
                    array_unshift($this->rules['route_path'], 'present');
                    // array_unshift($this->rules['is_hidden'], 'present');
                    //component_path可填入
                    break;
                case Models\Permissions::TYPE_PAGE:
                    array_unshift($this->rules['is_iframe'], 'present');
                    if (($data['is_iframe'] ?? false)) {
                        array_unshift($this->rules['frame_src'], 'present');
                    }
                    array_unshift($this->rules['route_path'], 'present');
                    array_unshift($this->rules['component_path'], 'present');
                    array_unshift($this->rules['no_cache'], 'present');
                    array_unshift($this->rules['is_hidden'], 'present');
                    break;
                case Models\Permissions::TYPE_PAGE:
                    break;
            }
        }

        $var = 1;
    }
}
