<?php

namespace App\Admin\Models;

use Illuminate\Database\Eloquent\Model;

class Account extends Model
{
    use \App\Api\Util\Query\ModelHelper;

    const TABLE_NAME = 'account';

    const STATUS_ENABLE = 1;
    const STATUS_DISABLE = 0;
    //已离职
    const STATUS_RESIGN = 2;
    //待审核
    const STATUS_WAIT_CONFIRM = 3;

    protected $table = self::TABLE_NAME;

    protected $casts = [
        'pids_blacklist' => 'array',
        'pidsBlackList' => 'array',
    ];

    protected $fillable = [
        'name', 'unionid', 'role_id', 'basic_dept_id', 'dept_id', 'username', 'department', 'avatar', 'telephone',
        'password', 'pids_blacklist', 'description', 'status'
    ];

    protected $whereHandler = [self::class, 'cookWheres'];

    public static function cookWheres($filterValue)
    {
        if (isset($filterValue['name'])) {
            $filterValue[] = ['name', 'like', '%' . $filterValue['name'] . '%'];
            unset($filterValue['name']);
        }

        return $filterValue;
    }
}
