<?php

namespace App\Admin\Services;

use App\Admin\Models;
use App\Exceptions\ServiceException;
use \Illuminate\Database\Eloquent\Collection as EloCollection;
use Illuminate\Support\Facades\DB;
use Firebase\JWT\Key;
use App\Api\Util\Enum\ErrorCode;

class Token
{
    /**
     * 关于jwt的介绍参考
     * https://www.ruanyifeng.com/blog/2018/07/json_web_token-tutorial.html
     */
    # 签发人
    protected const DEFAULT_ISS = 'SYS_API';
    # 签受众
    protected const DEFAULT_AUD = 'SYS_SPA';
    # 默认签名方法
    protected const DEFAULT_ALGORITHM = 'HS256';

    public static function getJwtKey()
    {
        return config('app.key');
    }

    public static function create($userId, $expired)
    {
        $randomCode = random_int(100000, 999999);

        $now = time();

        $payload = [
            'iat' => $now,
            'nbf' => $now,
            'exp' => $expired,
            'rcode' => $randomCode,
            'uid' => $userId
        ];

        $key = self::getJwtKey();

        //生成token
        $token = \Firebase\JWT\JWT::encode($payload, $key, self::DEFAULT_ALGORITHM);

        //有问题就抛错
        // throw new \App\Exceptions\ServiceException('找不到用户', 1000);

        return [$token, $randomCode];
    }

    public static function getPayload($jwt)
    {
        try {
            $raw = \Firebase\JWT\JWT::decode($jwt, new Key(self::getJwtKey(), self::DEFAULT_ALGORITHM));

            $payload = json_decode(json_encode($raw), true);
        } catch (\Firebase\JWT\SignatureInvalidException $e) {
            throw new ServiceException('Token校检失败', ErrorCode::TOKEN_INVALID);
        } catch (\Firebase\JWT\ExpiredException $e) {
            throw new ServiceException('Token已经失效', ErrorCode::TOKEN_EXPIRE);
        } catch (\Firebase\JWT\BeforeValidException $e) {
            throw new ServiceException('Token未到可用的时间', ErrorCode::TOKEN_BEFORE_VALID);
        } catch (\UnexpectedValueException $e) {
            throw new ServiceException('不合法的Token', ErrorCode::TOKEN_VERIFY_FAIL);
        }

        if (!isset($payload['uid']) || !isset($payload['rcode'])) {
            throw new ServiceException('缺少重要信息', ErrorCode::TOKEN_PAYLOAD_INVALID);
        }

        return [$payload['uid'], $payload['rcode']];
    }
}
