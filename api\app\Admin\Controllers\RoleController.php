<?php

namespace App\Admin\Controllers;

use App\Api\Util\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Admin\Models;
use App\Admin\Services;
use App\Api\Util\User;
use App\Api\Util\Enum\ValidationRule;

class RoleController extends \App\Api\Controllers\ApiController
{
    public function getPageList(Request $request)
    {
        $req = self::paramsFilter($request->input(), [
            'status', ['roleName', 'name']
        ]);

        $validator = new \App\Admin\Validator\Role();
        $result = $validator->otherRules(ValidationRule::PAGINATION)->check($req);

        $page = $request->input('page', 1);
        $pageSize = $request->input('pageSize', 10);

        $where = $req;

        //开发人员和超管的账号貌似不应该显示出来
        Services\Role::withoutHiddenRole($where, 'id');

        $query = Models\Roles::useDiyWheres($where);


        $collection = Services\Role::getCollection($query, [
            'id', 'idname as roleValue', 'name as roleName', 'parent_id as parentId', 'permissions',
            'status', 'created_at as createTime', 'note as remark'
        ], [], [
            'offset' => ($page - 1) * $pageSize,
            'limit' => $pageSize
        ]);
        $list = $collection->all();

        $total = $query->count('id');

        return ApiResponse::pagination($list, $total);
    }

    public function getList(Request $request)
    {
        $user = User::getInstance();
        $collection = $user->getRolesInfo([
            'id', 'id as key', 'name', 'parent_id as parentId'
        ]);

        return ApiResponse::success($collection);
    }

    public function create(Request $request)
    {
        $req = self::paramsFilter($request->post(), [
            ['roleValue', 'idname'], ['roleName', 'name'], ['parentId', 'parent_id'],
            ['remark', 'note'], 'status', 'permissions'
        ]);

        //检查参数
        $validator = new \App\Admin\Validator\Role();
        $result = $validator->musts([
            'idname', 'parent_id', 'name', 'status', 'permissions'
        ])->check($req);

        if ($req['parent_id'] != Services\Role::ROLE_ID_SUPER_ADMIN) {
            return ApiResponse::error(100000, '只能创建以超管为上级角色的角色');
        }

        $model = Models\Roles::create($req);
        if (!$model->save()) {
            return ApiResponse::error(100000, '创建失败');
        }

        $id = $model->id;
        return ApiResponse::success([
            'id' => $id
        ]);
    }

    public function update(Request $request)
    {
        //不能修改父级id
        $req = self::paramsFilter($request->post(), [
            'id', ['roleValue', 'idname'], ['roleName', 'name'],
            'status', 'permissions', ['remark', 'note']
        ]);

        //检查参数
        $validator = new \App\Admin\Validator\Role();
        $result = $validator->musts([
            'id', 'status'
        ])->check($req);

        //准备用户信息
        $user = User::getInstance();

        //如果要操作开发人员角色，将跳过permission的修改
        if ($req['id'] == Services\Role::ROLE_ID_DEVELOPER) {
            unset($req['permissions']);
        }

        if (isset($req['permissions'])) {
            //排序一下id
            sort($req['permissions']);
        }

        //更新
        DB::beginTransaction();
        try {
            //保证只可以修改自己拥有的角色
            $where = [['id', '=', $req['id']]];
            if ($user->roleId != Services\Role::ROLE_ID_DEVELOPER) {
                $where[] = ['parent_id', '=', $user->roleId];
            }

            $result = Models\Roles::where($where)->update($req);

            if ($result == 0) {
                DB::commit();
                return ApiResponse::error(100000, '找不到对应的对象');
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            return ApiResponse::error(100000, '更新失败');
        }

        //要去刷新权限
        Services\Account::updateUserPermissionByRoleId($req['id']);

        return ApiResponse::success(null);
    }

    public function detail(Request $request)
    {
        $req = [
            'id' => $request->get('id'),
        ];

        $validator = new \App\Admin\Validator\Role();
        $result = $validator->check($req);

        $info = Models\Roles::find($req['id'], [
            'id', 'name as roleName', 'idname as roleValue', 'parent_id as parentId', 'status', 'permissions'
        ]);

        if (empty($info)) {
            return ApiResponse::error(100000, '找不到对应的对象');
        }

        return ApiResponse::success($info);
    }

    public function delete(Request $request)
    {
        $req = [
            'id' => $request->get('id')
        ];

        $validator = new \App\Admin\Validator\Role();
        $result = $validator->check($req);

        //不能删除一个还有用户引用的角色
        $count = Models\Account::where([
            ['role_id', '=', $req['id']]
        ])->count('id');

        if ($count > 0) {
            return ApiResponse::error(100000, '不能删除一个还有用户引用的角色');
        }

        $result = Models\Roles::where([
            ['id', '=', $req['id']]
        ])->delete();

        if ($result == 0) {
            return ApiResponse::error(100000, '找不到对应的对象');
        }

        return ApiResponse::success($req['id']);
    }
}
