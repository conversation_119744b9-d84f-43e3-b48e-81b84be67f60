<?php
namespace App\CRM\Controllers\product;

use App\CRM\Services;
use App\Api\Util\User;
use Illuminate\Http\Request;
use App\Api\Util\ApiResponse;
use Illuminate\Support\Facades\DB;
use App\Api\Util\Enum\ValidationRule;

/**
 * 产品列表
 */
class ManageController extends \App\Api\Controllers\ApiController
{
    public function getPageList(Request $request)
    {
        $req = $request->all();

        $rep = Services\AdminSDK\ProductManage::getPageList('CRM', $req, $request->header('Authorization'));

        return ApiResponse::success($rep);
    }

    public function create(Request $request)
    {
        $req = self::paramsFilter($request->post(), [
            'name', 'icon', 'color', 'process_type_id' ,'type', 'system',
            'department_id', 'sort', 'entity', 'entity_field', 'note', 'status'
        ]);
        $req['status'] = Models\ProcessSet::STATUS_ENABLE;
        $req['entity'] = $req['entity']??'';
        $req['entity_field'] = $req['entity_field']??'';
        $req['note'] = $req['note']??'';
        $req['department_id'] = $req['department_id']??0;
        $req['sort'] = $req['sort']??0;
        //检查参数
        $validator = new \App\Management\Validator\ProcessSet();
        $validator->musts([
            'name', 'icon', 'color' ,'type', 'system',
            'note', 'status'
        ])->check($req);

        $model = Models\ProcessSet::create($req);

        if (!$model)
            return ApiResponse::error(100000, '创建失败');

        return ApiResponse::success($model);
    }

    public function detail(Request $request)
    {
        $req = $request->all();

        $rep = Services\AdminSDK\ProductBarcode::getPageList('CRM', $req, $request->header('Authorization'));

        return ApiResponse::success($rep);

        $req = self::paramsFilter($request->input(), [
            'id'
        ]);
        $req['id'] = 12;

        $validator = new \App\Management\Validator\ProcessSet();
        $validator->musts(['id'])->check($req);

        $info = Models\ProcessSet::find($req['id']);

        if (empty($info))
            return ApiResponse::error(100000, '数据不存在或已被删除');

        return ApiResponse::success($info);
    }

    public function delete(Request $request)
    {
        $req = self::paramsFilter($request->post(), [
            'id'
        ]);

        $validator = new \App\Management\Validator\ProcessSet();

        $validator->musts(['id'])->check($req);

        $result = Models\ProcessSet::withCount('processMain')->find($req['id']);

        if(empty($result))
            return ApiResponse::success($req['id']);

        if($result->process_main_count>0)
            return ApiResponse::error(100000, '不能删除进行中的流程');

        $result->delete();

        return ApiResponse::success($req['id']);
    }
}
