<?php

namespace App\Api\Util;

use App\Admin\Models;
use App\Admin\Services;
use App\Exceptions\ServiceException;
use \Illuminate\Database\Eloquent\Collection as EloCollection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;

class User
{
    protected const SESSION_ACCOUNT_PERMISSION = '_perm';

    const FIELD_FOR_USERMODLE = [
        'id as userId', 'role_id as roleId', 'name',
        'basic_dept_id as basicDeptId', 'dept_id as deptId',
        'pids_blacklist as pidsBlackList', 'description'
    ];

    const FIELD_FOR_ROLE = [
        'id', 'name as roleName', 'idname as value',
    ];

    const FIELD_FOR_DEPARTMENT = [
        'id', 'name as deptName', 'parent_id as parentId'
    ];

    const FIELD_FOR_PERMISSION = [
        'id'
    ];

    /**
     * @var \App\Api\Util\User
     */
    private static $instance;

    /**
     * @var array[int]
     */
    private $permissionIds;

    /**
     * @var \App\Admin\Models\Account
     */
    private $_data;

    public static function login($username, $password): self
    {
        return self::create([
            'username' => $username,
            'password' => $password,
            'status' => Models\Account::STATUS_ENABLE,
        ]);
    }

    public static function createByUserId($userId): self
    {
        return self::create([
            'id' => $userId,
            'status' => Models\Account::STATUS_ENABLE,
        ]);
    }

    protected static function create($filterValue): self
    {
        $query = Models\Account::useDiyWheres($filterValue);

        $collection = Services\Account::getCollection($query, self::FIELD_FOR_USERMODLE);

        if ($collection->isEmpty()) {
            throw new ServiceException('账户或密码错误', 100000);
        }

        $userInfo = $collection->shift();

        $userInfo = json_decode(json_encode($userInfo));
        $myself = new self($userInfo);
        self::$instance = $myself;

        return $myself;
    }


    public static function getInstance()
    {
        if (!isset(self::$instance)) {
            throw new ServiceException('请先登陆', 100000);
        }

        return self::$instance;
    }

    /**
     * @param \App\Admin\Models\Account
     */
    protected function __construct($user)
    {
        //按需填充好内容
        $this->_data = $user;
    }

    public function setPermissions($permissionIds)
    {
        $this->permissionIds = $permissionIds;
    }

    public function __get($name)
    {
        return ($this->_data->$name) ?? null;
    }

    public function getDepartmentInfo($fields = self::FIELD_FOR_DEPARTMENT): EloCollection
    {
        if (
            $this->roleId == Services\Role::ROLE_ID_DEVELOPER
            || $this->roleId == Services\Role::ROLE_ID_SUPER_ADMIN
        ) {
            //超管和开发人员能看到所有部门
            $query = [];
        } else {
            //根据这个最后一个id，去获取部门信息
            $query = Models\Department::useDiyWheres([
                'status' => Models\Department::STATUS_ENABLE,
                'pidstr' => $this->basicDeptId
            ], [
                'id' => $this->basicDeptId
            ]);
        }

        return Services\Department::getCollection($query, $fields);;
    }

    public function getRolesInfo($fields = self::FIELD_FOR_ROLE): EloCollection
    {
        //由于这里是白名单模式，所以不用Role::withoutHiddenRole

        if ($this->roleId == Services\Role::ROLE_ID_DEVELOPER) {
            //如果开发人员
            $query = [];
        } elseif ($this->roleId == Services\Role::ROLE_ID_SUPER_ADMIN) {
            $query = [
                ['id', '>', Services\Role::ROLE_ID_DEVELOPER]
            ];
        } else {
            //非开发人员就只能看到自己或者自己直接下属的角色
            $query = Models\Roles::useDiyWheres([
                'parent_id' => $this->roleId,
                'status' => models\Roles::STATUS_ENABLE,
            ], [
                'id' => $this->roleId
            ]);
        }

        return Services\Role::getCollection($query, $fields);
    }

    public function getPermissionIds(): array
    {
        if (!empty($this->permissionIds)) {
            return $this->permissionIds;
        }

        $collection = self::getPermissionsInfo();
        $this->permissionIds = Arr::pluck($collection->all(), 'id');
        return $this->permissionIds;
    }

    public function getPermissionsInfo($fields = self::FIELD_FOR_PERMISSION): EloCollection
    {
        !in_array('id', $fields) && array_unshift($fields, 'id');

        /**
         * 如果之前已经有去筛选过权限id，这样读取保持当前登陆内权限一致
         *
         * 要么就是调用self::getPermissionIds获取的
         * 要么就是通过loginInfo获取的id
         */
        if (!empty($this->permissionIds)) {
            $query = Models\Permissions::useDiyWheres([
                'id' => $this->permissionIds
            ]);

            $collection = Services\Permission::getCollection($query, $fields);
        }

        //先获取角色的权限
        $collection = Services\Permission::getCollectionByRoleId($this->roleId, $fields);

        //除了开发都需要过滤
        if ($this->roleId != Services\Role::ROLE_ID_DEVELOPER) {
            if (!empty($this->pidsBlackList)) {
                //然后根据user_id，blacklist过滤
                $blackListIds = $this->pidsBlackList;

                $collection = $collection->filter(function ($value, $key) use ($blackListIds) {
                    return !in_array($value->id, $blackListIds);
                });
            }
        }

        return $collection;
    }

    public function getMenuForPage(): array
    {
        /**
         * 具体标签的作用参考
         * https://doc.vvbin.cn/guide/router.html#meta-%E9%85%8D%E7%BD%AE%E8%AF%B4%E6%98%8E
         */
        $pcollection = $this->getPermissionsInfo([
            'id', 'parent_id', 'title', 'type', 'order', 'icon',
            'route_path', 'component_path', 'api',
            'is_hidden', 'is_iframe', 'frame_src', 'no_cache'
        ]);
        $pcollection = $pcollection->sortBy('order');

        return Services\Permission::cook2Menu($pcollection);
    }
}
