<?php

namespace App\Admin\Models;

use Illuminate\Database\Eloquent\Model;

class Permissions extends Model
{
    use \App\Api\Util\Query\ModelHelper;

    const TABLE_NAME = 'permissions';

    //这3个属性跟vben那边的
    const TYPE_MENU = 0;
    const TYPE_PAGE = 1;
    const TYPE_ITEM = 2;

    const STATUS_ENABLE = 1;
    const STATUS_DISABLE = 0;

    protected $table = self::TABLE_NAME;

    public $timestamps = false;

    protected $fillable = [
        'name', 'title', 'type', 'parent_id', 'order', 'icon',
        'is_hidden', 'is_iframe', 'frame_src',
        'route_path', 'component_path', 'no_cache', 'api', 'status'
    ];
}
