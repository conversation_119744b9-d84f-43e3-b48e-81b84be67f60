<?php

namespace App\Api\Util;

use app\Exceptions\ValidationException;


class ApiResponse
{
    public const CODE_SUCCESS = 0;

    public const TYPE_SUCCESS = 'success';
    public const TYPE_ERROR = 'error';
    public const TYPE_WARNING = 'warning';

    public static function pagination($list, $total)
    {
        return self::success([
            'items' => $list,
            'total' => $total
        ]);
    }

    public static function validationError($errors)
    {
        return self::error(ValidationException::DEFAULT_CODE, ValidationException::DEFAULT_TIP, $errors);
    }

    public static function success($data, $message = 'ok')
    {
        return self::createRaw(self::CODE_SUCCESS, self::TYPE_SUCCESS, $message, $data);
    }

    //部分成功时使用这个
    public static function warning($data, $message)
    {
        return self::createRaw(self::CODE_SUCCESS, self::TYPE_WARNING, $message, $data);
    }

    public static function error($code, $message = 'error', $errMsg = [])
    {
        //如果有code就去读取一个国际化的返回
        return self::createRaw($code, self::TYPE_ERROR, $message, null, $errMsg);
    }

    public static function createRaw($code, $type, $message, $data = null, $errMsg = [])
    {
        $rep = [
            'code' => $code,
            'message' => $message,
            'type' => $type,
        ];

        //只要不是成功的返回，有具体错误信息则返回
        if ($type == self::TYPE_ERROR && !empty($errMsg)) $rep['errMsg'] = $errMsg;
        //如果有设置返回信息
        if (isset($data)) $rep['result'] = $data;

        return response()->json($rep);
    }
}
