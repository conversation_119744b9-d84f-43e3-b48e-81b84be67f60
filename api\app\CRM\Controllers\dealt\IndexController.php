<?php
namespace App\CRM\Controllers\dealt;

use App\CRM\Services;
use App\Api\Util\User;
use App\Management\Models;
use Illuminate\Http\Request;
use App\Api\Util\ApiResponse;
use Illuminate\Support\Facades\DB;
use App\Api\Util\Enum\ValidationRule;

//待办列表
class IndexController extends \App\Api\Controllers\ApiController
{
    public function getPageList(Request $request)
    {
        $req = $params = $request->all();

        $rep = Services\AdminSDK\DealtIndex::getPageList('CRM', $req, $request->header('Authorization'));

        $dealt = $rep['items']??[];

        $processSet = Models\ProcessSet::whereIn('id',array_keys($dealt)??[])->get();


        $menuList = $processSet->map(function($item) use($dealt){
            $data = [];
            $data['name'] = $item->name;
            $data['icon'] = $item->icon;
            $data['color'] = $item->color;
            $data['type'] = $item->type;
            $data['route'] = $item->uniapp_url;
            $data['num'] = $dealt[$item->id]['nums']??0;
            return $data;
        })->toArray();

        return ApiResponse::success(['menuList'=>$menuList]);

        $menuList = [];

        $menuList[0]['name'] = "待跟进线索";
        $menuList[0]['type'] = "thread";
        $menuList[0]['num'] = 0;
        $menuList[0]['icon'] = '/static/crm/thread.png';
        $menuList[0]['route'] = 'dealtthread';

        $menuList[1]['name'] = "待跟进客户";
        $menuList[1]['type'] = "customers";
        $menuList[1]['num'] = 1;
        $menuList[1]['icon'] = '/static/crm/kehu1.png';
        $menuList[1]['route'] = 'dealtcustomers';

        $menuList[2]['name'] = "待跟进项目";
        $menuList[2]['type'] = "projects";
        $menuList[2]['num'] = 2;
        $menuList[2]['icon'] = '/static/crm/thread.png';
        $menuList[2]['route'] = 'dealtprojects';

        $menuList[3]['name'] = "待跟进商机";
        $menuList[3]['type'] = "businesses";
        $menuList[3]['num'] = 3;
        $menuList[3]['icon'] = '/static/crm/shangji.png';
        $menuList[3]['route'] = 'dealtbusinesses';

        $menuList[4]['name'] = "待跟进报价单";
        $menuList[4]['type'] = "price";
        $menuList[4]['num'] = 0;
        $menuList[4]['icon'] = '/static/crm/baojiadan.png';
        $menuList[4]['route'] = 'dealtprice';

        $menuList[5]['name'] = "待跟进合同";
        $menuList[5]['type'] = "contract";
        $menuList[5]['num'] = 0;
        $menuList[5]['icon'] = '/static/crm/hetong.png';
        $menuList[5]['route'] = 'dealtcontract';

        $menuList[6]['name'] = "待跟进回款计划";
        $menuList[6]['type'] = "payplan";
        $menuList[6]['num'] = 0;
        $menuList[6]['icon'] = '/static/crm/hkjh.png';
        $menuList[6]['route'] = 'dealtpayplan';

        $menuList[7]['name'] = "待跟进回款单";
        $menuList[7]['type'] = "payreceipt";
        $menuList[7]['num'] = 0;
        $menuList[7]['icon'] = '/static/crm/huikuan.png';
        $menuList[7]['route'] = 'dealtpayreceipt';
        return ApiResponse::success(['menuList'=>$menuList,'totalNum'=>10]);
    }

    public function create(Request $request)
    {
        $req = self::paramsFilter($request->post(), [
            'name', 'icon', 'color', 'process_type_id' ,'type', 'system',
            'department_id', 'sort', 'entity', 'entity_field', 'note', 'status'
        ]);
        $req['status'] = Models\ProcessSet::STATUS_ENABLE;
        $req['entity'] = $req['entity']??'';
        $req['entity_field'] = $req['entity_field']??'';
        $req['note'] = $req['note']??'';
        $req['department_id'] = $req['department_id']??0;
        $req['sort'] = $req['sort']??0;
        //检查参数
        $validator = new \App\Management\Validator\ProcessSet();
        $validator->musts([
            'name', 'icon', 'color' ,'type', 'system',
            'note', 'status'
        ])->check($req);

        $model = Models\ProcessSet::create($req);

        if (!$model)
            return ApiResponse::error(100000, '创建失败');

        return ApiResponse::success($model);
    }

    public function update(Request $request)
    {
        $req = self::paramsFilter($request->post(), [
            'id', 'name', 'icon', 'color', 'process_type_id' ,'type', 'system',
            'department_id', 'sort', 'entity', 'entity_field', 'note', 'status', 'form_data'
        ]);

        //检查参数
        $validator = new \App\Management\Validator\ProcessSet();
        $validator->musts([
            'id'
        ])->check($req);

        $result = Models\ProcessSet::find($req['id']);;

        if(empty($result))
            return ApiResponse::error(100000, '数据不存在或已被删除');

        return ApiResponse::success($result->update($req));
    }

    public function detail(Request $request)
    {
        $req = self::paramsFilter($request->input(), [
            'id'
        ]);

        $validator = new \App\Management\Validator\ProcessSet();
        $validator->musts(['id'])->check($req);

        $info = Models\ProcessSet::find($req['id']);

        if (empty($info))
            return ApiResponse::error(100000, '数据不存在或已被删除');

        return ApiResponse::success($info);
    }

    public function info(Request $request)
    {
        $req = self::paramsFilter($request->input(), [
            'system','entity'
        ]);

        $validator = new \App\Management\Validator\ProcessSet();
        $validator->musts(['system', 'entity'])->check($req);

        $info = Models\ProcessSet::where($req)->first();

        if (empty($info))
            return ApiResponse::error(100000, '数据不存在或已被删除');

        return ApiResponse::success($info);
    }

    public function delete(Request $request)
    {
        $req = self::paramsFilter($request->post(), [
            'id'
        ]);

        $validator = new \App\Management\Validator\ProcessSet();

        $validator->musts(['id'])->check($req);

        $result = Models\ProcessSet::withCount('processMain')->find($req['id']);

        if(empty($result))
            return ApiResponse::success($req['id']);

        if($result->process_main_count>0)
            return ApiResponse::error(100000, '不能删除进行中的流程');

        $result->delete();

        return ApiResponse::success($req['id']);
    }

    public function getClientList(Request $request)
    {
        $sysList = array_keys(\App\Management\Services\AdminSDK\Util::getSystem());

        return ApiResponse::success($sysList);
    }
}
