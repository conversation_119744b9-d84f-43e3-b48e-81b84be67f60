<?php

namespace App\Admin\Services;

use App\Admin\Models;
use App\Exceptions\ServiceException;
use \Illuminate\Database\Eloquent\Collection as EloCollection;
use Illuminate\Support\Facades\DB;
use Firebase\JWT\Key;
use App\Api\Util\Enum\ErrorCode;
use Error;
use Illuminate\Support\Arr;
use App\Api\Util\User;
use App\Api\Util\QueryHelper;

class AccountLoginInfo
{
    const EXPIRE_IN = 43200;

    public static function getByUserIdAndCode($userId, $code)
    {
        $collection = self::getCollection([
            'random_code' => $code,
            'account_id' => $userId
        ], [
            'token_expired', 'perms', 'p_status', 'updated_at'
        ]);

        if ($collection->isEmpty()) {
            throw new ServiceException('找不到账户登陆信息，请重新登陆', ErrorCode::LOGIN_INFO_NOT_FOUND);
        }

        $loginInfo = $collection->shift();
        return $loginInfo;
    }

    public static function refreshExpiredPermissions($userId, $permissionIds)
    {
        //写入新的权限状态
        //可以由 过期 变成 可用
        $result = DB::table(Models\AccountLoginInfo::TABLE_NAME)
            ->where([
                ['account_id', '=', $userId],
                ['p_status', '=', Models\AccountLoginInfo::P_STATUS_EXPIRED]
            ])->update([
                'perms' => $permissionIds,
                'p_status' => Models\AccountLoginInfo::P_STATUS_ENABLE
            ]);

        return $result > 0;
    }

    public static function logout($userId)
    {
        $result = DB::table(Models\AccountLoginInfo::TABLE_NAME)
            ->where([
                ['account_id', '=', $userId],
            ])->update([
                'token_expired' => date('Y-m-d H:i:s'),
            ]);

        return $result > 0;
    }

    public static function getCollection($query, $fields, $optionFields = [], $option = []): \Illuminate\Database\Eloquent\Collection
    {
        if (is_array($query)) {
            $query = Models\AccountLoginInfo::useDiyWheres($query);
        } else {
            //避免受到下面的查询干扰
            $query = clone $query;
        }

        $query->setFields($fields);

        $query->queryOption($option);

        return $query->get();
    }

    public static function refreshTokenByUid($userId)
    {
        $now = time(); //签发时间
        $expired = $now + self::EXPIRE_IN;

        [$token, $randomCode] = Token::create($userId, $expired);

        $result = MOdels\AccountLoginInfo::useDiyWheres([
            'account_id' => $userId
        ])->update([
            'random_code' => $randomCode,
            'token_expired' => date('Y-m-d H:i:s', $expired),
        ]);

        if (!$result) {
            throw new ServiceException('刷新登陆信息失败', ErrorCode::LOGIN_INFO_REFRESH_FAIL);
        }

        return $token;
    }

    public static function createTokenByInfo($userId, $permissionIds)
    {
        $now = time(); //签发时间
        $expired = $now + self::EXPIRE_IN;

        [$token, $randomCode] = Token::create($userId, $expired);

        $result = DB::table(Models\AccountLoginInfo::TABLE_NAME)
            ->updateOrInsert([
                'account_id' => $userId
            ], [
                'random_code' => $randomCode,
                'token_expired' => date('Y-m-d H:i:s', $expired),
                'perms' => json_encode($permissionIds)
            ]);

        if (!$result) {
            throw new ServiceException('创建登陆信息失败', ErrorCode::LOGIN_INFO_CREATE_FAIL);
        }

        return $token;
    }

    public static function createInfoByToken($token)
    {
        //先用token在logininfo表兑换用户信息
        [$userId, $randomCode] = Token::getPayload($token);

        $loginInfo = self::getByUserIdAndCode($userId, $randomCode);

        //创建一个用户服务，为了后面做准备
        $user = User::createByUserId($userId);

        $pStatus = $loginInfo->p_status;
        $permissionIds = $loginInfo->perms;

        $tokenExpired = strtotime($loginInfo->token_expired);
        if ($tokenExpired <= time()) {
            //被业务性地token过期了
            throw new ServiceException('账户需要重新登陆', ErrorCode::TOKEN_EXPIRE);
        }

        if ($pStatus == Models\AccountLoginInfo::P_STATUS_DISABLE) {
            //如果权限被禁止了，直接拒绝访问
            throw new ServiceException('账户被限制权限中', ErrorCode::PERMISSION_RESTICTED);
        } elseif ($pStatus == Models\AccountLoginInfo::P_STATUS_EXPIRED) {
            $permissionIds = $user->getPermissionIds();

            //权限过期刷新，但是无视结果
            self::refreshExpiredPermissions($userId, $permissionIds);
        }

        //缓存一下权限信息
        $user->setPermissions($permissionIds);

        return [$userId, $permissionIds];
    }
}
