<?php

namespace App\Api\Util\Query;

use \App\Api\Util\Query\QueryBuilder;

trait ModelHelper
{
    /**
     * 侵入式修改原本model返回的builder类型
     *
     * @param  \Illuminate\Database\Query\Builder  $query
     * @return \App\Api\Util\Query\QueryBuilder
     */
    public function newEloquentBuilder($query)
    {
        return new QueryBuilder($query);
    }

    /**
     * 创建一个自定义的QueryBuilder
     * @return \App\Api\Util\Query\QueryBuilder
     */
    public static function newDiyQuery()
    {
        $model = new static;
        $query = $model->newQueryWithoutRelationships();
        if (!empty($model->whereHandler)) {
            $query->setWhereHandler($model->whereHandler);
        }
        return $query;
    }

    /**
     * @param array $filterValue 查询条件，传入的规则跟据laravel的
     */
    public static function useDiyWheres(...$filterArray)
    {
        $query = self::newDiyQuery();

        foreach ($filterArray as $filterValue) {
            $query->setWheres($filterValue);
        }

        return $query;
    }
}
