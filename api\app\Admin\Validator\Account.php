<?php

namespace App\Admin\Validator;

use Illuminate\Validation\Rule;
use App\Admin\Models;

class Account extends \App\Api\Util\CustomerValidator
{

    public function __construct()
    {
        $this->rules = [
            'id' => ['integer'],
            'unionid' => ['string'],
            'role_id' => ['integer'],
            'name' => ['string', 'max:190'],
            'basic_dept_id' => ['integer'],
            'dept_id' => ['integer'],
            'avatar' => ['url'],
            'telephone' => ['string'],
            'password' => ['string', 'max:60'],
            'description' => ['string', 'max:255'],
            'status' => [
                Rule::in([
                    Models\Account::STATUS_DISABLE,
                    Models\Account::STATUS_ENABLE,
                    Models\Account::STATUS_RESIGN,
                    Models\Account::STATUS_WAIT_CONFIRM,
                ]),
            ],
        ];
    }
}
