<?php
namespace App\Api\Util;

use think\facade\Cache;

class WxMessage
{
	//小程序发送一次性订阅消息
	/*
	参数实例:
	{
		//用户ID列表，指定为"@all"，则向该企业应用的全部成员发送
	   "touser" : "UserID1|UserID2|UserID3",
	   //部门ID列表
	   "toparty" : "PartyID1|PartyID2",
	   //标签ID 当touser为"@all"时忽略本参数
	   "totag" : "TagID1 | TagID2",
	   //消息类型，此时固定为：text
	   "msgtype" : "text",
	   //企业应用的id，整型。企业内部开发，可在应用的设置页面查看
	   "agentid" : 1,
	   //消息内容，最长不超过2048个字节，超过将截断
	   "text" : {
	       "content" : "你的快递已到，请携带工卡前往邮件中心领取。\n出发前可查看<a href=\"http://work.weixin.qq.com\">邮件中心视频实况</a>，聪明避开排队。"
	   },
	   //表示是否是保密消息，0表示可对外分享，1表示不能分享且内容显示水印，默认为0
	   "safe":0,
	   //表示是否开启id转译，0表示否，1表示是，默认0。仅第三方应用需要用到，企业自建应用可以忽略。
	   "enable_id_trans": 0,
	   //表示是否开启重复消息检查，0表示否，1表示是，默认0
	   "enable_duplicate_check": 0,
	   //表示是否重复消息检查的时间间隔，默认1800s，最大不超过4小时
	   "duplicate_check_interval": 1800
	}
	*/
	public function sendMessage($data = [])
	{
		$data['msgtype'] = $data['msgtype']??'text';
		$data['agentid'] = $data['agentid']??env('WEWORK_CORID');

	    //请求url
	    $url = sprintf(config('weixin.seso.send_message_url'), (self::getAccessToken()));

	    $result = self::curlPost($url,$data);
		return true;
	}


	/**
	 * 小程序用户的openid获得企业微信的userid
	 */
	public function openIdToUserId($openId)
	{
		$url = sprintf(config('weixin.seso.openid_userid_url'), (self::getAccessToken()));

		$result = json_decode(self::curlPost($url,['openid'=>$openId]), true);
		return $result['userid']??'';
	}

	/**
	 * 用户手机号获取企业微信userid
	 */
	public function mobileToUserId($mobile)
	{
		$url = sprintf(config('weixin.seso.mobile_userid_url'), (self::getAccessToken()));

		$result = json_decode(self::curlPost($url,['mobile'=>$mobile]), true);
		return $result['userid']??'';
	}


	//发送post请求 小程序发送一次性订阅消息
	// public function curlPost($url,$data)
	// {
	// 	$ch = curl_init();
	// 	$params[CURLOPT_URL] = $url;    //请求url地址
	// 	$params[CURLOPT_HEADER] = FALSE; //是否返回响应头信息
	// 	$params[CURLOPT_SSL_VERIFYPEER] = false;
	// 	$params[CURLOPT_SSL_VERIFYHOST] = false;
	// 	$params[CURLOPT_RETURNTRANSFER] = true; //是否将结果返回
	// 	$params[CURLOPT_POST] = true;
	// 	$params[CURLOPT_POSTFIELDS] = $data;
	// 	curl_setopt_array($ch, $params); //传入curl参数
	// 	$content = curl_exec($ch); //执行
	// 	curl_close($ch); //关闭连接
	// 	return $content;
	// }

	/**
	 * @param string $url post请求地址
	 * @param array $params
	 * @return mixed
	 */
	public function curlPost($url, array $params = [])
	{
	    $data_string = json_encode($params);
	    $ch = curl_init();
	    curl_setopt($ch, CURLOPT_URL, $url);
	    curl_setopt($ch, CURLOPT_HEADER, 0);
	    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
	    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
	    curl_setopt($ch, CURLOPT_POST, 1);
	    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
	    curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
	    curl_setopt(
	        $ch, CURLOPT_HTTPHEADER,
	        array(
	            'Content-Type: application/json'
	        )
	    );
	    $data = curl_exec($ch);
	    curl_close($ch);
	    return ($data);
	}

	/**
	 * @param string $url get请求地址
	 * @param int $httpCode 返回状态码
	 * @return mixed
	 */
	function curlGet($url, &$httpCode = 0)
	{
	    $ch = curl_init();
	    curl_setopt($ch, CURLOPT_URL, $url);
	    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

	    //不做证书校验,部署在linux环境下请改为true
	    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
	    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
	    $file_contents = curl_exec($ch);
	    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
	    curl_close($ch);
	    return $file_contents;
	}

	/**
	  * AccessToken 保留时间2小时  7200
	  * @return mixed
	  */
	public function getAccessToken()
	{
		$result = Cache::get('miniAccessToken');

		if(empty($result)||empty($result['access_token']))
		{
			$wxAppID = config('weixin.seso.corp_id');
			$wxAppSecret = config('weixin.seso.app_secret');
			$wxAccessTokenUrl = sprintf(config('weixin.seso.access_token_url'), $wxAppID, $wxAppSecret);

			$result = json_decode(file_get_contents($wxAccessTokenUrl),true);
			// 缓存在7000秒之后过期
			Cache::set('miniAccessToken', $result, 7000);
		}

		return $result['access_token']??'';
	}

	public function getOpenId($code)
	{
		//login_url
		$url = sprintf(config('weixin.seso.login_url'), config('weixin.seso.mini_app_id'), config('weixin.seso.mini_app_secret'), $code);

		return json_decode(self::curlPost($url,['code'=>$code]), true);
	}

    public function getVisitOpenId($code)
    {
        //login_url
        $url = sprintf(config('weixin.visit.login_url'), config('weixin.visit.mini_app_id'), config('weixin.visit.mini_app_secret'), $code);

        return json_decode(self::curlPost($url,['code'=>$code]), true);
    }

	//思索制造openId
	public function getPrdOpenId($code)
	{
		//login_url
		$url = sprintf(config('weixin.base.login_url'), config('weixin.prd.mini_app_id'), config('weixin.prd.mini_app_secret'), $code);

		return json_decode(self::curlPost($url,['code'=>$code]), true);
	}

	//思索供应商openId
	public function getSupplierOpenId($code)
	{
		//login_url
		$url = sprintf(config('weixin.base.login_url'), config('weixin.supplier.mini_app_id'), config('weixin.supplier.mini_app_secret'), $code);

		return json_decode(self::curlPost($url,['code'=>$code]), true);
	}
}
