<?php
namespace App\Api\Controllers;

use App\Admin\Models;
use App\Api\Util\User;
use App\Admin\Services;
use Illuminate\Http\Request;
use App\Api\Util\ApiResponse;
use App\CRM\Models as CRMModels;
use Illuminate\Support\Facades\DB;
use App\CRM\Services as CRMServices;
use App\Api\Util\Enum\ValidationRule;

//公共API
class CommonController extends \App\Api\Controllers\ApiController
{
    protected $userInfo = ['roleId' => 1];
    /**
     * 获取菜单列表
     * @param {Object} Request $request
     */
    public function getMenuTree()
    {
        //返回一个tree，用于select框
        //返回当前用户可以看到的权限
        User::createByUserData($this->userInfo);
        $user = User::getInstance();

        $collection = $user->getPermissionsInfo([
            'id',
            'id as key',
            'parent_id as parentId',
            'name',
            'icon'
        ]);

        $tree = Services\Util::cook2Tree($collection, [
            'parent' => 'parentId',
            'child' => 'id',
            'root' => 0
        ]);

        $role = $user->getRolesInfo([
            'id',
            'id as key',
            'name',
            'parent_id as parentId'
        ]);

        return ApiResponse::success(['menu' => $tree, 'role' => $role]);
    }

    /**
     * 获得角色列表
     * @param {Object} Request $request
     */
    public function getRoleList(Request $request)
    {
        User::createByUserData($this->userInfo);
        $user = User::getInstance();
        $collection = $user->getRolesInfo([
            'id',
            'id as key',
            'name',
            'parent_id as parentId'
        ]);

        return ApiResponse::success($collection);
    }

    /**
     * 获得角色列表，按分页
     * @param {Object} Request $request
     */
    public function getRolePageList(Request $request)
    {
        $req = self::paramsFilter($request->input(), [
            'status',
            ['roleName', 'name']
        ]);

        $validator = new \App\Admin\Validator\Role();
        $result = $validator->otherRules(ValidationRule::PAGINATION)->check($req);

        $page = $request->input('page', 1);
        $pageSize = $request->input('pageSize', 10);

        $where = $req;

        //创建登入动作
        User::createByUserData($this->userInfo);
        $user = User::getInstance();

        //开发人员和超管的账号貌似不应该显示出来
        Services\Role::withoutHiddenRole($where, 'id');

        $query = Models\Roles::useDiyWheres($where);


        $collection = Services\Role::getCollection($query, [
            'id',
            'idname as roleValue',
            'name as roleName',
            'parent_id as parentId',
            'permissions',
            'status',
            'created_at as createTime',
            'note as remark'
        ], [], [
            'offset' => ($page - 1) * $pageSize,
            'limit' => $pageSize
        ]);
        $list = $collection->all();

        $total = $query->count('id');

        return ApiResponse::pagination($list, $total);
    }

    public function createRole(Request $request)
    {
        $req = self::paramsFilter($request->post(), [
            ['roleValue', 'idname'],
            ['roleName', 'name'],
            ['parentId', 'parent_id'],
            ['remark', 'note'],
            'status',
            'permissions'
        ]);

        //检查参数
        $validator = new \App\Admin\Validator\Role();
        $result = $validator->musts([
            'idname',
            'parent_id',
            'name',
            'status',
            'permissions'
        ])->check($req);

        if ($req['parent_id'] != Services\Role::ROLE_ID_SUPER_ADMIN) {
            //return ApiResponse::error(100000, '只能创建以超管为上级角色的角色');
        }

        $model = Models\Roles::create($req);
        if (!$model->save()) {
            return ApiResponse::error(100000, '创建失败');
        }

        $id = $model->id;
        return ApiResponse::success([
            'id' => $id
        ]);
    }

    public function updateRole(Request $request)
    {
        //不能修改父级id
        $req = self::paramsFilter($request->post(), [
            'id',
            ['roleValue', 'idname'],
            ['roleName', 'name'],
            'status',
            'permissions',
            ['remark', 'note']
        ]);

        //检查参数
        $validator = new \App\Admin\Validator\Role();
        $result = $validator->musts([
            'id',
            'status'
        ])->check($req);

        //如果要操作开发人员角色，将跳过permission的修改
        if ($req['id'] == Services\Role::ROLE_ID_DEVELOPER) {
            unset($req['permissions']);
        }

        if (isset($req['permissions'])) {
            //排序一下id
            sort($req['permissions']);
        }

        //更新
        DB::beginTransaction();
        try {
            //保证只可以修改自己拥有的角色
            $where = [['id', '=', $req['id']]];

            $result = Models\Roles::where($where)->update($req);

            if ($result == 0) {
                DB::commit();
                return ApiResponse::error(100000, '找不到对应的对象');
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            return ApiResponse::error(100000, '更新失败');
        }

        //要去刷新权限
        Services\Account::updateUserPermissionByRoleId($req['id']);

        return ApiResponse::success(null);
    }

    public function deleteRole(Request $request)
    {
        $req = [
            'id' => $request->get('id')
        ];

        $validator = new \App\Admin\Validator\Role();
        $result = $validator->check($req);

        //不能删除一个还有用户引用的角色
        $count = Models\Account::where([
            ['role_id', '=', $req['id']]
        ])->count('id');

        if ($count > 0) {
            return ApiResponse::error(100000, '不能删除一个还有用户引用的角色');
        }

        $result = Models\Roles::where([
            ['id', '=', $req['id']]
        ])->delete();

        if ($result == 0) {
            return ApiResponse::error(100000, '找不到对应的对象');
        }

        return ApiResponse::success($req['id']);
    }

    /**
     * 获得关联部门列表
     * @param {Object} Request $request
     */
    public function getRoleDeptList(Request $request) {}

    /**
     * 获得人员列表
     * @param {Object} Request $request
     */
    public function getPersonList(Request $request)
    {
        $params = self::getParams($request);


        return ApiResponse::success([]);
    }

    /**
     * 获得部门列表
     * @param {Object} Request $request
     */
    public function getDeptList(Request $request)
    {
        $user = User::getInstance();
        $params = self::getParams($request);

        return ApiResponse::success([]);
    }

    /**
     * 获得客户列表
     * @param {Object} Request $request
     */
    public function getCustomList(Request $request)
    {
        $params = self::getParams($request);

        $list = (new CRMServices\CustomersService())->getCommonList($params, (new CRMModels\CustomersModel()));

        return ApiResponse::success($list);
    }

    /**
     * 获得项目列表
     * @param {Object} Request $request
     */
    public function getProjectList(Request $request)
    {
        $params = self::getParams($request);

        $list = (new CRMServices\ProjectsService())->getCommonList($params, (new CRMModels\ProjectsModel()));

        return ApiResponse::success($list);
    }

    /**
     * 获得国家列表
     * @param {Object} Request $request
     */
    public function getCountriesList(Request $request)
    {
        $params = self::getParams($request);

        $list = (new CRMServices\CountryService())->getCommonList($params, (new CRMModels\CountryModel()));

        return ApiResponse::success($list);
    }
}
