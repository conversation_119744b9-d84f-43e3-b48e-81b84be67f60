<?php

namespace App\Management\Controllers;

use App\Api\Util\ApiResponse;
use Illuminate\Http\Request;
use App\Management\Models;
use App\Management\Services;
use App\Management\Util\User;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;

class OpenApiController extends \App\Api\Controllers\ApiController
{
    private $key = '_11111000001111@';

    /**
     * 移动端登入
     */
    public function login(Request $request)
    {
        $req = self::paramsFilter($request->post(), [
            'username',
            'password',
            'code',
            'avatarUrl'
        ]);

        if (!empty($req['code'])) {
            //通过企业微信CODE快速登入
            $rep = Services\AdminSDK\QyWeiXin::getMiniUserInfo(['code' => $req['code']]);

            if (empty($rep['userid']))
                return ApiResponse::error(100000, '用户授权错误');

            //判断数据表里是否有数据
            $account = Models\Account::useDiyWheres([['username', '=', $rep['userid']]], [['wxworkId', '=', $rep['userid']]])->first();


            if (empty($account))
                return ApiResponse::error(100000, '企业微信成员未获取');

            if (!empty($req['avatarUrl'])) {
                $account->avatar = $req['avatarUrl'];

                $account->save();
            }

            //检查数据库的用户信息
            $user = User::login($account->username, $account->password);
        } else {
            //账号密码登入
            $validator = new \App\Management\Validator\Account();
            $validator->musts([
                'username',
                'password'
            ])->check($req);

            $user = User::login($req['username'], md5($req['username'] . $req['password'] . $this->key));
        }

        $accountSysRel = $user->accountSysRel ? json_decode(json_encode($user->accountSysRel), true) : [];
        //dd($user->getData());
        //准备好token信息
        $token = Services\AccountLoginInfo::createTokenByInfo($user->userId, [], $user->getData());
        $userInfo = [];
        $userInfo['userId'] = $user->userId;
        $userInfo['name'] = $user->name;
        $userInfo['username'] = $user->username;
        $userInfo['wxworkId'] = $user->wxworkId;
        $userInfo['deptId'] = $user->deptId;
        $userInfo['accountSysRel'] = $user->accountSysRel;
        $userInfo['avatar'] = $user->avatar ?? '';
        $userInfo['depType'] = $user->depType;
        $userInfo['deptLeader'] = $user->deptLeader;

        $menu = [];

        if ($accountSysRel) {
            $where = [];
            $where[] = ['status', '=', Models\PermissionsApp::STATUS_ENABLE];
            $where[] = ['parent_id', '=', 0];
            $permissionsApp = Models\PermissionsApp::with(['children'])
                ->where($where)
                ->whereIn('system', array_keys($accountSysRel))
                ->get();

            // dd($permissionsApp);
        }


        $menu['ERP'] = [];
        $menu['ERP'][0]['name'] = '销售管理';
        $menu['ERP'][0]['child'][] = ['name' => '销售订单', 'ename' => 'saleorder', 'url' => '/pages1/order/index', 'color' => 'icon-color01', 'icon' => 'icon-jiabanshenpi'];
        $menu['ERP'][0]['child'][] = ['name' => '销售出库', 'ename' => 'saleoutbound', 'url' => '/pages1/outbound/index', 'color' => 'icon-color03', 'icon' => 'icon-youhuiquan'];
        $menu['ERP'][0]['child'][] = ['name' => '销售退货', 'ename' => 'salerefund', 'url' => '/pages1/refund/index', 'color' => 'icon-color02', 'icon' => 'icon-jiabanshenpi'];
        //$menu['CRM'][1]['child'][] = ['name'=>'销售收款', 'ename'=>'saleorder', 'url'=> '/pages1/receivable/index', 'color'=>'icon-color04', 'icon'=>'icon-jiabanshenpi'];

        $menu['ERP'][1]['name'] = '财务管理';
        $menu['ERP'][1]['child'] = [];
        $menu['ERP'][1]['child'][] = ['name' => '回款计划', 'ename' => 'hkjh', 'url' => '/pages1/payplan/index', 'color' => 'icon-color10', 'icon' => 'icon-haocaifei'];
        $menu['ERP'][1]['child'][] = ['name' => '实际回款', 'ename' => 'sjhk', 'url' => '/pages1/payreal/index', 'color' => 'icon-color11', 'icon' => 'icon-huiyishi1'];

        $menu['ERP'][2] = [];
        $menu['ERP'][2]['name'] = '采购管理';
        $menu['ERP'][2]['child'] = [];
        $menu['ERP'][2]['child'][] = ['name' => '采购订单', 'ename' => 'order', 'url' => '/pages2/order/index', 'color' => 'icon-color01', 'icon' => 'icon-jiabanshenpi'];
        $menu['ERP'][2]['child'][] = ['name' => '采购入库', 'ename' => 'storage', 'url' => '/pages2/storage/index', 'color' => 'icon-color02', 'icon' => 'icon-yongche'];
        $menu['ERP'][2]['child'][] = ['name' => '采购退货', 'ename' => 'returned', 'url' => '/pages2/returned/index', 'color' => 'icon-color03', 'icon' => 'icon-baoming'];
        $menu['ERP'][2]['child'][] = ['name' => '采购付款', 'ename' => 'payment', 'url' => '/pages2/payment/index', 'color' => 'icon-color04', 'icon' => 'icon-qingjia'];
        $menu['ERP'][2]['child'][] = ['name' => '供应商管理', 'ename' => 'supplier', 'url' => '/pages2/supplier/index', 'color' => 'icon-color07', 'icon' => 'icon-jiabanshenpi'];
        $menu['ERP'][2]['child'][] = ['name' => '收货通知单', 'ename' => 'supplierarrears', 'url' => '/pages2/receivebill/index', 'color' => 'icon-color06', 'icon' => 'icon-message-one'];
        $menu['ERP'][2]['child'][] = ['name' => '供应商往来', 'ename' => 'suppliertransactions', 'url' => '/pages2/supplierdealings/index', 'color' => 'icon-color05', 'icon' => 'icon-gongnengdingyi'];
        $menu['ERP'][2]['child'][] = ['name' => '供应商欠款', 'ename' => 'supplierarrears', 'url' => '/pages2/supplierarrears/index', 'color' => 'icon-color08', 'icon' => 'icon-kongxinduigou'];

        $menu['ERP'][3] = [];
        $menu['ERP'][3]['name'] = '费用管理';
        $menu['ERP'][3]['child'] = [];
        $menu['ERP'][3]['child'][] = ['name' => '其它支出单', 'ename' => 'otherpay', 'url' => '/pages2/otherpay/index', 'color' => 'icon-color01', 'icon' => 'icon-hetongxieyi'];


        $menu['ERP'][4] = [];
        $menu['ERP'][4]['name'] = '仓储管理';
        $menu['ERP'][4]['child'] = [];
        $menu['ERP'][4]['child'][] = ['name' => '包裹管理', 'ename' => 'salepackage', 'url' => '/pages1/package/index', 'color' => 'icon-color05', 'icon' => 'icon-tupian'];
        $menu['ERP'][4]['child'][] = ['name' => '装箱管理', 'ename' => 'encasement', 'url' => '/pages1/encasement/index', 'color' => 'icon-color05', 'icon' => 'icon-baoming'];
        $menu['ERP'][4]['child'][] = ['name' => '入库管理', 'ename' => 'warehousing', 'url' => '/pages2/warehousing/index', 'color' => 'icon-color08', 'icon' => 'icon-tianjia'];
        $menu['ERP'][4]['child'][] = ['name' => '仓库管理', 'ename' => 'storagemanage', 'url' => '/pages2/storagemanage/index', 'color' => 'icon-color06', 'icon' => 'icon-shiyanshikaohe'];
        $menu['ERP'][4]['child'][] = ['name' => '仓位管理', 'ename' => 'storagelocation', 'url' => '/pages2/storagelocation/index', 'color' => 'icon-color01', 'icon' => 'icon-ribao'];
        $menu['ERP'][4]['child'][] = ['name' => '备货管理', 'ename' => 'storageup', 'url' => '/pages2/storageup/index', 'color' => 'icon-color11', 'icon' => 'icon-hetongxieyi'];
        //        $menu['ERP'][4]['child'][] = ['name'=>'其它入库', 'ename'=>'storageother', 'url'=> '/pages2/storageother/index', 'color'=>'icon-color08', 'icon'=>'icon-tianjia'];
        $menu['ERP'][4]['child'][] = ['name' => '转仓调拨', 'ename' => 'storagechange', 'url' => '/pages2/storagechange/index', 'color' => 'icon-color03', 'icon' => 'icon-chucha'];
        $menu['ERP'][4]['child'][] = ['name' => '库存调整', 'ename' => 'storageadjustment', 'url' => '/pages2/storageadjustment/index', 'color' => 'icon-color05', 'icon' => 'icon-daibanshixiang2'];
        $menu['ERP'][4]['child'][] = ['name' => '库存盘点', 'ename' => 'storageinventory', 'url' => '/pages2/storageinventory/index', 'color' => 'icon-color02', 'icon' => 'icon-bianjisekuai'];
        $menu['ERP'][4]['child'][] = ['name' => '库存查询', 'ename' => 'storagequery', 'url' => '/pages2/storagequery/index', 'color' => 'icon-color09', 'icon' => 'icon-hetongguanli'];
        $menu['ERP'][4]['child'][] = ['name' => '账龄统计', 'ename' => 'storageaging', 'url' => '/pages2/storageaging/index', 'color' => 'icon-color11', 'icon' => 'icon-huiyishi'];
        // $menu['PIMS'] = [];
        // $menu['PIMS'][0] = [];
        // $menu['PIMS'][0]['name'] = '选品';
        // $menu['PIMS'][0]['child'] = [];
        // $menu['PIMS'][0]['child'][0] = ['name'=>'PI设计', 'ename'=>'design', 'url'=> '/pims/design/index', 'color'=>'icon-color12', 'icon'=>'icon-baoming1'];
        // $menu['PIMS'][0]['child'][1] = ['name'=>'PI管理', 'ename'=>'manage', 'url'=> '/pims/manage/index', 'color'=>'icon-color13', 'icon'=>'icon-jiabanshenqing'];
        // $menu['PIMS'][0]['child'][2] = ['name'=>'PI订单', 'ename'=>'order', 'url'=> '/pims/order/index', 'color'=>'icon-color02', 'icon'=>'icon-hetongxieyi'];
        // $menu['PIMS'][0]['child'][3] = ['name'=>'商城预览', 'ename'=>'shop', 'url'=> '/pims/chanpin/shop', 'color'=>'icon-color03', 'icon'=>'icon-jiabanshenpi'];

        // $menu['PDMS'] = [];
        // $menu['PDMS'][0] = [];
        // $menu['PDMS'][0]['name'] = '常用功能';
        // $menu['PDMS'][0]['child'] = [];
        // $menu['PDMS'][0]['child'][0] = ['name'=>'产品列表', 'ename'=>'client', 'url'=> '/pdms/product/index', 'color'=>'icon-color04', 'icon'=>'icon-yongche'];
        // $menu['PDMS'][0]['child'][1] = ['name'=>'产品分类', 'ename'=>'followUp', 'url'=> '/pdms/category/index', 'color'=>'icon-color05', 'icon'=>'icon-baoming'];
        // $menu['PDMS'][0]['child'][2] = ['name'=>'产品图片', 'ename'=>'business', 'url'=> '/pdms/imgs/index', 'color'=>'icon-color06', 'icon'=>'icon-qingjia'];



        //$menu['CRM'][0] = [];
        //$menu['CRM'][0]['name'] = '待办事项';
        //$menu['CRM'][0]['child'] = [];
        //$menu['CRM'][0]['child'][] = ['name'=>'待跟客户', 'ename'=>'dealtclient', 'url'=> '/pages1/dealt/customers/index', 'color'=>'icon-color10', 'icon'=>'icon-plan'];
        //$menu['CRM'][0]['child'][] = ['name'=>'待跟项目', 'ename'=>'dealtprojects', 'url'=> '/pages1/dealt/projects/index', 'color'=>'icon-color05', 'icon'=>'icon-moban'];
        //$menu['CRM'][0]['child'][] = ['name'=>'待跟联系人', 'ename'=>'dealtcontacts', 'url'=> '/pages1/dealt/contacts/index', 'color'=>'icon-color11', 'icon'=>'icon-a-time1'];
        //$menu['CRM'][0]['child'][] = ['name'=>'待跟商机', 'ename'=>'dealtbusiness', 'url'=> '/pages1/dealt/businesses/index', 'color'=>'icon-color12', 'icon'=>'icon-zanting'];
        //$menu['CRM'][0]['child'][] = ['name'=>'待跟报价单', 'ename'=>'dealtprice', 'url'=> '/pages1/dealt/price/index', 'color'=>'icon-color13', 'icon'=>'icon-xiangqingliebiao'];
        //$menu['CRM'][0]['child'][] = ['name'=>'待跟合同', 'ename'=>'dealtcontract', 'url'=> '/pages1/dealt/contract/index', 'color'=>'icon-color10', 'icon'=>'icon-workbench'];
        //$menu['CRM'][0]['child'][] = ['name'=>'回款计划', 'ename'=>'dealtpayplan', 'url'=> '/pages1/dealt/payplan/index', 'color'=>'icon-color02', 'icon'=>'icon-shouji'];
        //$menu['CRM'][0]['child'][] = ['name'=>'回款提醒', 'ename'=>'contract', 'url'=> '/pages1/dealt/hetong/hetong', 'color'=>'icon-color05', 'icon'=>'icon-message-one'];
        //$menu['CRM'][0]['child'][] = ['name'=>'回款单', 'ename'=>'dealtpayreceipt', 'url'=> '/pages1/dealt/payreceipt/index', 'color'=>'icon-color03', 'icon'=>'icon-faan'];
        //$menu['CRM'][0]['child'][] = ['name'=>'临期合同', 'ename'=>'contract', 'url'=> '/pages1/dealt/hetong/hetong', 'color'=>'icon-color01', 'icon'=>'icon-peoples'];
        $menu['CRM'][0]['name'] = '客户管理';
        $menu['CRM'][0]['child'] = [];
        $menu['CRM'][0]['child'][] = ['name' => '线索池', 'ename' => 'threadC', 'url' => '/pages1/thread/pool', 'color' => 'icon-color01', 'icon' => 'icon-tupian'];
        $menu['CRM'][0]['child'][] = ['name' => '线索', 'ename' => 'thread', 'url' => '/pages1/thread/index', 'color' => 'icon-color02', 'icon' => 'icon-xiangji'];
        $menu['CRM'][0]['child'][] = ['name' => '客户池', 'ename' => 'clientGh', 'url' => '/pages1/customers/pool', 'color' => 'icon-color03', 'icon' => 'icon-zengjia'];
        $menu['CRM'][0]['child'][] = ['name' => '客户', 'ename' => 'client', 'url' => '/pages1/customers/index', 'color' => 'icon-color04', 'icon' => 'icon-youhuiquan'];
        $menu['CRM'][0]['child'][] = ['name' => '联系人', 'ename' => 'linkman', 'url' => '/pages1/contacts/index', 'color' => 'icon-color05', 'icon' => 'icon-quanbudingdan'];
        $menu['CRM'][0]['child'][] = ['name' => '跟进记录', 'ename' => 'followUp', 'url' => '/pages1/followup/index', 'color' => 'icon-color06', 'icon' => 'icon-moban'];

        $menu['CRM'][1]['name'] = '销售管理';
        $menu['CRM'][1]['child'] = [];
        $menu['CRM'][1]['child'][] = ['name' => '项目', 'ename' => 'projects', 'url' => '/pages1/projects/index', 'color' => 'icon-color06', 'icon' => 'icon-baoming'];
        $menu['CRM'][1]['child'][] = ['name' => '商机', 'ename' => 'business', 'url' => '/pages1/business/index', 'color' => 'icon-color07', 'icon' => 'icon-hetongqianzi'];
        $menu['CRM'][1]['child'][] = ['name' => '报价单', 'ename' => 'quotation', 'url' => '/pages1/prices/index', 'color' => 'icon-color08', 'icon' => 'icon-gongwujiedai'];
        $menu['CRM'][1]['child'][] = ['name' => '合同', 'ename' => 'contract', 'url' => '/pages1/contract/index', 'color' => 'icon-color09', 'icon' => 'icon-kaoqinchuqin'];

        $menu['CRM'][2]['name'] = '产品管理';
        $menu['CRM'][2]['child'] = [];
        $menu['CRM'][2]['child'][] = ['name' => '产品列表', 'ename' => 'product', 'url' => '/pages1/product/index', 'color' => 'icon-color03', 'icon' => 'icon-haocaifei'];
        $menu['CRM'][2]['child'][] = ['name' => '产品条码', 'ename' => 'productbarcode', 'url' => '/pages1/barcode/index', 'color' => 'icon-color06', 'icon' => 'icon-youhuiquan'];



        $menu['AUTOERP'][0] = [];
        $menu['AUTOERP'][0]['name'] = '费用管理';
        $menu['AUTOERP'][0]['child'] = [];
        $menu['AUTOERP'][0]['child'][] = ['name' => '其它支出单', 'ename' => 'otherpay', 'url' => '/pages2/otherpay/index?_system=AUTOERP', 'color' => 'icon-color01', 'icon' => 'icon-hetongxieyi'];

        $menu['CARERP'][0] = [];
        $menu['CARERP'][0]['name'] = '费用管理';
        $menu['CARERP'][0]['child'] = [];
        $menu['CARERP'][0]['child'][] = ['name' => '其它支出单', 'ename' => 'otherpay', 'url' => '/pages2/otherpay/index?_system=CARERP', 'color' => 'icon-color01', 'icon' => 'icon-hetongxieyi'];


        $dept = [];
        $dept['CRM'] = [];
        $dept['CRM'][0] = [['id' => 1, 'name' => '门窗部'], ['id' => 2, 'name' => '电器部'], ['id' => 3, 'name' => '软装部'], ['id' => 4, 'name' => '金玻部']];
        $dept['ERP'] = [];
        $dept['ERP'][0] = [['id' => 1, 'name' => '家具部'], ['id' => 2, 'name' => '设计部'], ['id' => 3, 'name' => '网销部'], ['id' => 4, 'name' => '灯饰一部']];
        $dept['ERP'] = [];
        $dept['ERP'][0] = [['id' => 1, 'name' => '灯饰二部'], ['id' => 2, 'name' => '电器部'], ['id' => 3, 'name' => '家具部'], ['id' => 4, 'name' => '软装部']];

        $res = new \stdClass();
        $res->menu = $menu;
        $res->dept = $user->deptPermissions;
        $res->user = $userInfo;
        $res->token = $token;

        return ApiResponse::success($res);
    }

    public function getToken(Request $request)
    {
        // 增加执行时间限制，防止复杂查询超时
        set_time_limit(300); // 设置为5分钟

        $req = self::paramsFilter($request->post(), [
            'username',
            'password',
            'key'
        ]);

        //如果是企业微信登入
        if (!empty($req['key'])) {
            $rep = Services\AdminSDK\QyWeiXin::getUserInfo(['code' => $req['key']]);

            if ((int)$rep['errcode'] > 0)
                return ApiResponse::error(100000, $rep['errmsg'] ?? '企业微信成员错误');

            //判断数据表里是否有数据
            $account = Models\Account::useDiyWheres([['username', '=', $rep['userid']]], [['wxworkId', '=', $rep['userid']]])->first();

            //没有则新建
            if (empty($account)) {
                $userInfo = Services\AdminSDK\QyWeiXin::getMember(['userid' => $rep['userid']]);

                if ((int)$rep['errcode'] > 0)
                    return ApiResponse::error(100000, $rep['errmsg'] ?? '企业微信成员未获取');

                $depId = $userInfo['department'][0] ?? 0;

                //获得本地部门信息
                $depInfo = Models\Department::where('wx_dep_id', $depId)->first();

                if (empty($depInfo))
                    return ApiResponse::error(100000, '无效的部门参数');

                $pidStr = explode('_', trim($depInfo->pidStr, '_'));
                $pidIndex = count($pidStr) >= 2 ? 2 : count($pidStr);

                $data = [];
                $data['name'] = $userInfo['name'] ?? '';
                $data['alias'] = $userInfo['alias'] ?? '';
                $data['username'] = $userInfo['userid'] ?? '';
                $data['wxworkId'] = $userInfo['userid'] ?? '';
                $data['status'] = 1;
                $data['dept_id'] = $depInfo->id ?? 0;
                $data['basic_dept_id'] = $pidStr[count($pidStr) - $pidIndex] ?? 0;

                $password = md5($data['username'] . '123456' . $this->key);
                $data['password'] = $password;
                $account = Models\Account::create($data);
            }

            if (empty($account->alias)) {
                $userInfo = Services\AdminSDK\QyWeiXin::getMember(['userid' => $rep['userid']]);
                $account->alias = $userInfo['alias'] ?? '';
                $account->save();
            }


            // $txtFileName = 'member.txt';
            // //以读写方式打写指定文件，如果文件不存则创建
            // if (($txtRes=fopen($txtFileName, "a+"))) {

            //     //将信息写入文件
            //     fwrite($txtRes, date('Y-m-d H:i:s').":".json_encode($userInfo)."\r\n");
            //     //关闭指针
            //     fclose($txtRes);
            // }

            //检查数据库的用户信息
            $user = User::login($account->username, $account->password);
        } else {


            $validator = new \App\Management\Validator\Account();
            $result = $validator->otherRules([
                'username' => ['string']
            ])->musts([
                'username',
                'password'
            ])->check($req);

            //检查数据库的用户信息
            $user = User::login($req['username'], $req['password']);
        }

        //准备好token信息
        $token = Services\AccountLoginInfo::createTokenByInfo($user->userId, [], $user->getData());
        $userInfo = new \stdClass();
        $userInfo->userId = $user->userId;
        $userInfo->token = $token;

        return ApiResponse::success($userInfo);
    }

    public function getClient(Request $request)
    {
        $user = User::getInstance();

        $page = $request->input('page', 1);
        $pageSize = 50; //$request->input('pageSize', 10);

        $accountSysRel = [];
        $where = [];
        $where[] = ['name', 'in', array_keys(json_decode(json_encode($user->accountSysRel), true))];

        $query = Models\Client::useDiyWheres($where);
        $collection = Services\Client::getCollection($query, [
            'id',
            'name',
            'server_url',
            'front_url',
            'description',
            'created_at as createTime'
        ], [], [
            'offset' => ($page - 1) * $pageSize,
            'limit' => $pageSize
        ]);

        $list = $collection->all();

        $total = $query->count('id');

        return ApiResponse::pagination($list, $total);
    }

    public function notice(Request $request)
    {
        // 假设企业号在公众平台上设置的参数如下
        $token = env('ENCODINGTOKEN');
        $corpId = env('WEWORK_CORID');
        $encodingAesKey = env('ENCODINGAESKEY');

        $wxcpt = new Services\WeiXinSDK\WxBizMsgCrypt($token, $encodingAesKey, $corpId);

        $sVerifyMsgSig = $request->input('msg_signature', '');
        $sVerifyTimeStamp = $request->input('timestamp', '');
        $sVerifyNonce = $request->input('nonce', '');
        $sVerifyEchoStr = $request->input('echostr', '');

        if ($request->isMethod('post'))
            $sVerifyEchoStr = file_get_contents('php://input');

        $sEchoStr = "";

        $params = $request->toArray();

        $txtFileName = "notifytest.txt";

        if ($request->isMethod('post')) {
            // call verify function
            $errCode = $wxcpt->DecryptMsg($sVerifyMsgSig, $sVerifyTimeStamp, $sVerifyNonce, $sVerifyEchoStr, $sEchoStr);
            //dd($errCode,$sEchoStr);
            if ($errCode == 0) {
                //以读写方式打写指定文件，如果文件不存则创建
                if (($txtRes = fopen($txtFileName, "a+"))) {

                    //将信息写入文件
                    fwrite($txtRes, date('Y-m-d H:i:s') . ":" . json_encode($params) . "\r\n");
                    fwrite($txtRes, date('Y-m-d H:i:s') . ":" . $sEchoStr . "\r\n");
                    //关闭指针
                    fclose($txtRes);
                }

                $xml = new \DOMDocument();
                $xml->loadXML($sEchoStr);

                $array_type = $xml->getElementsByTagName('ChangeType');
                $changeType = $array_type->item(0)->nodeValue ?? '';
                if (!empty($changeType)) {
                    //部门
                    if (
                        $changeType == 'create_party' ||
                        $changeType == 'update_party' ||
                        $changeType == 'delete_party'
                    ) {
                        $array_id = $xml->getElementsByTagName('Id');
                        $id = $array_id->item(0)->nodeValue;
                        switch ($changeType) {
                            //创建部门
                            case 'create_party':
                                //更新部门
                            case 'update_party':
                                Log::info($id);
                                Log::info($changeType);
                                //从微信获得详细信息
                                $depInfo = Services\AdminSDK\QyWeiXin::getDepartment(['id' => (int)$id]);
                                $depInfo = $depInfo['department'] ?? [];
                                if ($depInfo) {
                                    $array_pid = $xml->getElementsByTagName('ParentId');
                                    $pid = $array_pid->item(0)->nodeValue;
                                    $ids = [(int)$id, (int)$pid];

                                    //查询自己与上级的信息
                                    $infos = Models\Department::whereIn('wx_dep_id', $ids)->get();

                                    $tmpInfos = [];
                                    foreach ($infos as $info) {
                                        $tmpInfos[$info->wx_dep_id] = $info;
                                    }

                                    $pidStr = $tmpInfos[$pid]->pidStr ?? "_";
                                    $data = [];
                                    $data['name'] = $depInfo['name'];
                                    $data['parent_id'] = $tmpInfos[$pid]->id ?? 0;
                                    $data['wx_dep_id'] = (int)$id;
                                    $data['status'] = 1;
                                    $data['pidStr'] = $pidStr . $data['parent_id'] . '_';

                                    Models\Department::updateOrCreate(['id' => $tmpInfos[(int)$id]->id ?? 0], $data);
                                }
                                break;
                            //删除部门
                            case 'delete_party':
                                //禁用本地部门信息
                                Models\Department::where('wx_dep_id', $id)->update(['status' => 0]);
                                break;
                        }
                    }

                    //标签
                    if ($changeType == 'update_tag') {
                        //从微信获同步标签列表
                        $tagInfo = Services\AdminSDK\QyWeiXin::getTagList();

                        foreach ($tagInfo['taglist'] ?? [] as $item) {
                            Models\AccountTags::updateOrCreate(['tagid' => $item['tagid']], $item);
                        }

                        $tag_id = $xml->getElementsByTagName('TagId');
                        $tagId = $tag_id->item(0)->nodeValue;

                        //新增
                        $addUserItems = $xml->getElementsByTagName('AddUserItems');
                        $addUsers = $addUserItems->item(0)->nodeValue ?? '';

                        //删除
                        $delUserItems = $xml->getElementsByTagName('DelUserItems');
                        $delUsers = $delUserItems->item(0)->nodeValue ?? '';

                        //Log::info($tagId);
                        //Log::info($addUsers);
                        //Log::info($delUsers);

                        $tag = Models\AccountTags::with(['accountTagsRel'])->where('tagid', $tagId)->first();

                        if ($tag) {
                            //如果有新增信息
                            if ($addUsers) {
                                $addUsers = explode(',', $addUsers);

                                $usersArr = [];
                                foreach ($addUsers as $userKey => $userVal) {
                                    $usersArr[$userKey] = [];
                                    $usersArr[$userKey]['tagid'] = $tagId;
                                    $usersArr[$userKey]['name'] = $userVal;
                                    $usersArr[$userKey]['userid'] = $userVal;
                                    $usersArr[$userKey]['created_at'] = date('Y-m-d H:i:s');
                                    $usersArr[$userKey]['updated_at'] = date('Y-m-d H:i:s');
                                }

                                //写入关联表
                                if ($usersArr)
                                    $tag->accountTagsRel()->insert($usersArr);
                            }

                            //如果有删除信息
                            if ($delUsers) {
                                $delUsers = explode(',', $delUsers);
                                $tag->accountTagsRel()->whereIn('userid', $delUsers)->delete();
                            }
                        }
                    }
                    //用户
                    if (
                        $changeType == 'create_user' ||
                        $changeType == 'update_user' ||
                        $changeType == 'delete_user'
                    ) {
                        $array_id = $xml->getElementsByTagName('UserID');
                        $userId = $array_id->item(0)->nodeValue;

                        switch ($changeType) {
                            //创建用户
                            case 'create_user':
                                //更新用户
                            case 'update_user':
                                //从微信获得详细信息
                                $userInfo = Services\AdminSDK\QyWeiXin::getMember(['userid' => $userId]);

                                if (!empty($userInfo['userid'])) {
                                    $depId = $userInfo['department'][0] ?? 0;
                                    //获得本地部门信息
                                    $depInfo = Models\Department::where('wx_dep_id', $depId)->first();

                                    $pidStr = explode('_', trim($depInfo->pidStr, '_'));
                                    $pidIndex = count($pidStr) >= 2 ? 2 : count($pidStr);

                                    $data = [];
                                    $data['name'] = $userInfo['name'] ?? '';
                                    $data['username'] = $userInfo['userid'] ?? '';
                                    $data['wxworkId'] = $userInfo['userid'] ?? '';
                                    $data['status'] = 1;
                                    $data['dept_id'] = $depInfo->id ?? 0;
                                    $data['basic_dept_id'] = !empty($pidStr[count($pidStr) - $pidIndex]) ? $pidStr[count($pidStr) - $pidIndex] : 1;

                                    $leaderIds = [];
                                    if (!empty($userInfo['department'])) {
                                        $depLeader = array_combine(array_values($userInfo['department']), array_values($userInfo['is_leader_in_dept']));

                                        //判断是否为部门负责人
                                        $isLeaderInDept = $userInfo['is_leader_in_dept'] ?? [];


                                        foreach ($depLeader as $leaderKey => $leaderItem) {
                                            if ($leaderItem == 1)
                                                $leaderIds[] = $leaderKey;
                                        }
                                    }


                                    $userInfo = Models\Account::useDiyWheres([
                                        'username' => $data['wxworkId'],
                                    ], [
                                        'wxworkId' => $data['wxworkId'],
                                    ])->first();

                                    if (!empty($userInfo)) {
                                        $userInfo->update($data);
                                    } else {
                                        $password = md5($data['username'] . '123456' . $this->key);
                                        $data['password'] = $password;
                                        $userInfo = Models\Account::create($data);
                                    }

                                    //如果是负责人，则更新部门负责人
                                    Models\Department::whereIn('leader_id', [$userInfo->id])->update(['leader_id' => 0]);

                                    if (!empty($leaderIds))
                                        Models\Department::whereIn('wx_dep_id', $leaderIds)->update(['leader_id' => $userInfo->id]);
                                }
                                break;
                            //删除用户
                            case 'delete_user':
                                //状态改为离职
                                Models\Account::where('wxworkId', $userId)->update(['status' => 2, 'departure_date' => date('Y-m-d')]);
                                break;
                        }
                    }
                }
            } else {
                //以读写方式打写指定文件，如果文件不存则创建
                if (($txtRes = fopen($txtFileName, "a+"))) {

                    //将信息写入文件
                    fwrite($txtRes, date('Y-m-d H:i:s') . ":" . json_encode($params) . "\r\n");
                    fwrite($txtRes, date('Y-m-d H:i:s') . "error:" . $sVerifyEchoStr . "\r\n");
                    //关闭指针
                    fclose($txtRes);
                }
            }
        } else {
            $errCode = $wxcpt->VerifyURL($sVerifyMsgSig, $sVerifyTimeStamp, $sVerifyNonce, $sVerifyEchoStr, $sEchoStr);
            if ($errCode == 0) {
                var_dump($sEchoStr);
                //
                // 验证URL成功，将sEchoStr返回
                // HttpUtils.SetResponce($sEchoStr);
            } else {
                //print("ERR: " . $errCode . "\n\n");
            }
        }
    }

    private function syncTags()
    {
        //从微信获同步标签列表
        $tagInfo = Services\AdminSDK\QyWeiXin::getTagList();
        foreach ($tagInfo['taglist'] ?? [] as $item) {
            //Models\AccountTags::updateOrCreate(['tagid'=>$item['tagid']], $item);

            //获得标签成员
            $tagMember = Services\AdminSDK\QyWeiXin::getTagMember($item['tagid']);

            $userlist = $tagMember['userlist'] ?? [];

            $tag = Models\AccountTags::with(['accountTagsRel'])->where('tagid', $item['tagid'])->first();

            //清空关联
            $tag->accountTagsRel()->delete();

            //写入关联。
            if (!empty($userlist)) {
                array_walk($userlist, function (&$v) use ($tag) {
                    $v['tagid'] = $tag->tagid;
                    $v['created_at'] = date('Y-m-d H:i:s');
                    $v['updated_at'] = date('Y-m-d H:i:s');
                });

                $tag->accountTagsRel()->insert($userlist);
            }
        }
    }

    private function delTags()
    {
        $tag = Models\AccountTags::with(['accountTagsRel'])->where('tagid', 1)->first();
        $tag->accountTagsRel()->whereIn('userid', ['LiuHui', 'raymond'])->delete();
        dd($tag);
    }

    public function miniopenid(Request $request)
    {
        $req = $request->toArray();

        //通过企业微信CODE快速登入
        $rep = Services\AdminSDK\QyWeiXin::getMiniUserInfo(['code' => $req['code']]);

        if (empty($rep['userid']))
            return ApiResponse::error(100000, '用户授权错误');

        //判断数据表里是否有数据
        $account = Models\Account::useDiyWheres([['username', '=', $rep['userid']]], [['wxworkId', '=', $rep['userid']]])->first();


        if (empty($account))
            return ApiResponse::error(100000, '未绑定企业微信，请用账密登入');

        return ApiResponse::success($rep);
    }

    public function updatainfo(Request $request)
    {
        $req = $request->toArray();

        //$account = Models\Account::useDiyWheres([['username','=',$rep['userid']]],[['wxworkId','=',$rep['userid']]])->first();

        $data = (new Models\AppVersionModel())->orderBy('version', 'desc')->first();

        return ApiResponse::success(['data' => $data]);
        return json([
            'code' => 200,
            'data' => $data ?? []
        ]);
    }
}
