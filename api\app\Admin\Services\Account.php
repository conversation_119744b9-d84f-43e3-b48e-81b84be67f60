<?php

namespace App\Admin\Services;

use App\Admin\Models;
use App\Exceptions\ServiceException;
use \Illuminate\Database\Eloquent\Collection as EloCollection;
use App\Api\Util\QueryHelper;


class Account
{
    public static function getCollection($query, $fields, $optionFields = [], $option = []): \Illuminate\Database\Eloquent\Collection
    {
        if (is_array($query)) {
            $query = Models\Account::useDiyWheres($query);
        } else {
            //避免受到下面的查询干扰
            $query = clone $query;
        }

        $query->setFields($fields);

        // //由于暂时没想好怎么识别" **.name as *** " 的格式字符串格式，故先写成这样
        if (in_array('roleName', $optionFields)) {
            $query->setJoin('left', Models\Roles::TABLE_NAME, function ($join) {
                $join->on(Models\Account::TABLE_NAME . '.role_id', '=', Models\Roles::TABLE_NAME . '.id');
            }, ['name as roleName']);
        }

        $query->queryOption($option);

        return $query->get();
    }

    public static function updateDepartmentInfo($departmentId, $newPidStr)
    {
        $accounts = self::getCollection([
            'department' => $departmentId
        ], [
            'id', 'department'
        ]);

        $accounts->map(function ($accountModel) use ($departmentId, $newPidStr) {
            $departments = $accountModel->deparment;
            $accountModel->deparment = preg_replace(
                "/(?:_[0-9]+_)*?{$departmentId}_/",
                $newPidStr . '_' . $departmentId . '_',
                $departments
            );
            $accountModel->save();
        });
    }

    public static function updateUserPermissionByRoleId($roleId)
    {
        $result = Models\AccountLoginInfo::whereIn('account_id', function ($query) use ($roleId) {
            return $query->select('id')->from(Models\Account::TABLE_NAME)->where('role_id', '=', $roleId);
        })->update([
            'p_status' => Models\AccountLoginInfo::P_STATUS_EXPIRED
        ]);

        return $result > 0;
    }
}
