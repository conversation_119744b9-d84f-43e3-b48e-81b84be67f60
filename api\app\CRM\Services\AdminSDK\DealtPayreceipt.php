<?php
namespace App\CRM\Services\AdminSDK;

use App\Management\Services\AdminSDK\RequestService;

class DealtPayreceipt
{
    /**
     * @param array $req 用于请求创建用户接口的参数
     */
    public static function getPageList($system, $req, $token)
    {
        $s = new RequestService($system, $token);
        return $s->get('/dealt/payreceipt/getPageList', $req);
    }

    public static function create($system, $req, $token)
    {
        $s = new RequestService($system, $token);
        return $s->postJson('/dealt/payreceipt/create', $req);
    }

    public static function detail($system, $req, $token)
    {
        $s = new RequestService($system, $token);
        return $s->get('/dealt/payreceipt/detail', $req);
    }

    public static function delete($system, $req, $token)
    {
        $s = new RequestService($system, $token);
        return $s->get('/dealt/payreceipt/delete', $req);
    }
}
