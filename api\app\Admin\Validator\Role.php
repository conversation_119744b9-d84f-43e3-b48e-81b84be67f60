<?php

namespace App\Admin\Validator;

use Illuminate\Validation\Rule;
use App\Admin\Models;

class Role extends \App\Api\Util\CustomerValidator
{
    public function __construct()
    {
        $this->rules = [
            'id' => ['integer'],
            'idname' => ['string', 'max:20'],
            'parent_id' => ['integer'],
            'name' => ['string', ''],
            'note' => ['string', 'max:255'],
            'status' => [
                Rule::in([
                    Models\Permissions::STATUS_DISABLE,
                    Models\Permissions::STATUS_ENABLE,
                ]),
            ],
            'permissions' => ['array']
        ];
    }
}
