<?php
namespace App\CRM\Services\AdminSDK;

use App\Management\Services\AdminSDK\RequestService;

class ProductBarcode
{
    /**
     * @param array $req 用于请求创建用户接口的参数
     */
    public static function getPageList($system, $req, $token)
    {
        $s = new RequestService($system, $token);
        return $s->get('/product/barcode/getPageList', $req);
    }

    public static function detail($system, $req, $token)
    {
        $s = new RequestService($system, $token);
        return $s->get('/product/barcode/detail', $req);
    }

    public static function create($system, $req, $token)
    {
        $s = new RequestService($system, $token);
        return $s->post('/product/barcode/create', $req);
    }

    public static function createdetail($system, $req, $token)
    {
        $s = new RequestService($system, $token);
        return $s->get('/product/barcode/createdetail', $req);
    }
}
