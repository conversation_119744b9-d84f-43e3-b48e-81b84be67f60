<?php
namespace App\ERP\Controllers\cost;

use App\ERP\Services;
use App\Api\Util\User;
use Illuminate\Http\Request;
use App\Api\Util\ApiResponse;
use Illuminate\Support\Facades\DB;
use App\Api\Util\Enum\ValidationRule;

/**
 * 其它支出单
 */
class OtherpayController extends \App\Api\Controllers\ApiController
{
    private $type = [1=>'个人报销', 2=>'款项支出', 3=>'财务费用'];
    private $isCheck = [0=>'待审核', 1=>'已审核', 2=>'已拒绝'];
    public function getPageList(Request $request)
    {
        $req = self::paramsFilter($request->all(), [
            '_system', 'page', 'pageSize'
        ]);

        $system = !empty($req['_system'])?$req['_system']:env('ERPFLAG');

        unset($req['_system']);

        $rep = Services\AdminSDK\CostOtherpay::getPageList($system, $req, $request->header('Authorization'));

        $rep['nav'] = [];
        $rep['nav']['columns'] = [];
        $rep['nav']['columns'][] = ['field'=>'creator_name', 'type'=>'text','title'=>'申请人'];
        $rep['nav']['columns'][] = ['field'=>'type', 'type'=>'radio','title'=>'类型', 'options'=>$this->type];
        $rep['nav']['columns'][] = ['field'=>'department', 'type'=>'text','title'=>'部门'];
        $rep['nav']['columns'][] = ['field'=>'amount', 'type'=>'number','title'=>'支出金额', 'digit'=>2];
        $rep['nav']['columns'][] = ['field'=>'created_at', 'type'=>'text','title'=>'创建时间'];

        return ApiResponse::success($rep);
    }

    public function create(Request $request)
    {
        $req = $request->all();

        $rep = Services\AdminSDK\CostOtherpay::create(env('ERPFLAG'), $req, $request->header('Authorization'));

        $rep['form'] = [];
        $rep['form']['type'] = $this->type;
        $res['is_check'] = $this->is_check;

        return ApiResponse::success($rep);
    }

    public function change(Request $request)
    {
        $req = self::paramsFilter($request->input(), [
            '_type', 'id', '_system'
        ]);

        $system = !empty($req['_system'])?$req['_system']:env('ERPFLAG');

        unset($req['_system']);


        $type = $req['_type']??0;

        switch($type)
        {
            //确认
            case 1:
                $rep = Services\AdminSDK\CostOtherpay::setStatus($system, ['id'=>$req['id']??0, 'status'=>1], $request->header('Authorization'));
                break;
            //主管审核
            case 2:
                $rep = Services\AdminSDK\CostOtherpay::setApprove($system, ['id'=>$req['id']??0, 'is_approve'=>1], $request->header('Authorization'));
                break;
            //财务审核
            case 3:
                $rep = Services\AdminSDK\CostOtherpay::setFinance($system, ['id'=>$req['id']??0, 'is_approve'=>1], $request->header('Authorization'));
                break;
        }
        return ApiResponse::success([]);
    }

    public function detail(Request $request)
    {
        $req = $request->all();

        $system = !empty($req['_system'])?$req['_system']:env('ERPFLAG');

        unset($req['_system']);

        $rep = Services\AdminSDK\CostOtherpay::detail($system, $req, $request->header('Authorization'));

        $rep['form'] = [];
        $rep['form']['type'] = $this->type;
        return ApiResponse::success($rep);
    }

    public function info(Request $request)
    {
        $req = self::paramsFilter($request->input(), [
            'system','entity'
        ]);

        $validator = new \App\Management\Validator\ProcessSet();
        $validator->musts(['system', 'entity'])->check($req);

        $info = Models\ProcessSet::where($req)->first();

        if (empty($info))
            return ApiResponse::error(100000, '数据不存在或已被删除');

        return ApiResponse::success($info);
    }

    public function delete(Request $request)
    {
        $req = self::paramsFilter($request->post(), [
            'id'
        ]);

        $validator = new \App\Management\Validator\ProcessSet();

        $validator->musts(['id'])->check($req);

        $result = Models\ProcessSet::withCount('processMain')->find($req['id']);

        if(empty($result))
            return ApiResponse::success($req['id']);

        if($result->process_main_count>0)
            return ApiResponse::error(100000, '不能删除进行中的流程');

        $result->delete();

        return ApiResponse::success($req['id']);
    }

    public function getClientList(Request $request)
    {
        $sysList = array_keys(\App\Management\Services\AdminSDK\Util::getSystem());

        return ApiResponse::success($sysList);
    }
}
