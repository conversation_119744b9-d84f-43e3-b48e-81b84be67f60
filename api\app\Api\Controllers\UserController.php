<?php

namespace App\Api\Controllers;

use App\Api\Util\ApiResponse;
use Illuminate\Http\Request;
use App\Admin\Models;
use App\Admin\Services;
use App\Api\Util\User;
use Illuminate\Support\Arr;

class UserController extends \App\Api\Controllers\ApiController
{
    public function login(Request $request)
    {
        $req = self::paramsFilter($request->post(), ['password', 'username']);

        //检查参数
        $validator = new \App\Admin\Validator\Account();
        $validatedReq = $validator->musts([
            'password', 'username'
        ])->otherRules([
            'test' => ['require']
        ])->check($req);

        //检查数据库的用户信息
        $user = User::login($req['username'], $req['password']);

        //新登陆，需要重新准备一次权限信息
        $pCollection = $user->getPermissionsInfo();
        $permissionIds = Arr::pluck($pCollection->all(), 'id');

        //准备好token信息
        $token = Services\AccountLoginInfo::createTokenByInfo($user->userId, $permissionIds);

        $userInfo = new \stdClass();
        $userInfo->userId = $user->userId;
        $userInfo->token = $token;

        return ApiResponse::success($userInfo);
    }


    public function regist(Request $request)
    {
        $req = self::paramsFilter($request->post(), [
            'name', 'username', 'password', ['remark', 'description']
        ]);

        $validator = new \App\Admin\Validator\Account();
        $result = $validator->musts([
            'name', 'username', 'password'
        ])->check($req);

        $flag = Models\Account::where($req)->exists();

        if ($flag) return ApiResponse::error(100000, '当前账号名已经存在');

        $req['status'] = Models\Account::STATUS_WAIT_CONFIRM;
        //挂载在无权限角色
        $req['role_id'] = Services\Role::ROLE_ID_NO_PERMISSION;
        //挂载在空部门上
        $req['basic_dept_id'] = $req['dept_id'] = Services\Department::ID_WAIT_CONFIRM;

        $model = Models\Account::create($req);
        if (!$model->save()) {
            return ApiResponse::error(100000, '注册信息登记失败');
        }

        $userInfo = new \stdClass();
        $userInfo->userId = $model->id;
        $userInfo->username = $req['username'];
        $userInfo->realName = $req['name'];

        return ApiResponse::success($userInfo);
    }

    public function resetPassword(Request $request)
    {
        $req = self::paramsFilter($request->post(), [
            'id', ['passwordNew', 'password'], 'passwordOld'
        ]);

        $validator = new \App\Admin\Validator\Account();
        $result = $validator->musts([
            'id', 'passwordOld', 'password'
        ])->otherRules([
            'passwordOld' => ['string', 'max:60'],
        ])->check($req);

        $result = Models\Account::where([
            ['id', '=', $req['id']],
            ['password', '=', $req['passwordOld']]
        ])->update([
            'password' => $req['password']
        ]);

        if ($result == 0) {
            return ApiResponse::error(100000, '更新失败');
        }

        $user = User::getInstance();

        Services\AccountLoginInfo::logout($user->userId);
        return ApiResponse::success([
            'userId' => $req['id']
        ], 'Token has been destroyed');
    }

    public function logout(Request $request)
    {
        $user = User::getInstance();
        Services\AccountLoginInfo::logout($user->userId);

        return ApiResponse::success(null, 'Token has been destroyed');
    }

    public function getUserInfo(Request $request)
    {
        $user = User::getInstance();

        $userInfo = new \stdClass();
        $userInfo->userId = $user->userId;
        $userInfo->roleId = $user->roleId;
        $userInfo->realName = $user->name;
        $userInfo->desc = $user->description;

        //准备好角色信息
        $roles = $user->getRolesInfo();

        $userInfo->roles = $roles;

        return ApiResponse::success($userInfo);
    }

    public function getMenuList(Request $request)
    {
        $user = User::getInstance();
        //一般默认是拿自己的menu（在初始化系统时会调用一次

        $menu = $user->getMenuForPage();

        return ApiResponse::success($menu);
    }

    public function getPermCodeList(Request $request)
    {
        $user = User::getInstance();
        //一般默认是拿自己的menu（在初始化系统时会调用一次

        $permissionIds = $user->getPermissionIds();

        return ApiResponse::success($permissionIds);
    }
}
