<?php
namespace App\CRM\Services\AdminSDK;

use App\Management\Services\AdminSDK\RequestService;

class ProductManage
{
    /**
     * @param array $req 用于请求创建用户接口的参数
     */
    public static function getPageList($system, $req, $token)
    {
        $s = new RequestService($system, $token);
        return $s->get('/product/getPageList', $req);
    }

    public static function detail($system, $req, $token)
    {
        $s = new RequestService($system, $token);
        return $s->get('/product/detail', $req);
    }
}
