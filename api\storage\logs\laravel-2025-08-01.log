[2025-08-01 15:07:44] local.DEBUG: account: {"username":"<PERSON><PERSON><PERSON><PERSON><PERSON>","password":"d3922302a49412cb302a4a50d08b2b17"} 
[2025-08-01 15:09:10] local.DEBUG: account: {"username":"<PERSON><PERSON><PERSON><PERSON><PERSON>","password":"46479703501cb552132b8aa16eb19bce"} 
[2025-08-01 15:10:10] local.ERROR: Maximum execution time of 60 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 60 seconds exceeded at C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:368)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Maximum execut...', '0', 'array ('type' =...', '0', '???', '???')
#1 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('array ('type' =...', '0')
#2 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(705): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:359-376}(''select `id`, `...', 'array (0 => 13,...')
#4 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\MySqlConnection->runQueryCallback(''select `id`, `...', 'array (0 => 13,...', 'class Closure {...')
#5 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(376): Illuminate\\Database\\MySqlConnection->run(''select `id`, `...', 'array (0 => 13,...', 'class Closure {...')
#6 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2414): Illuminate\\Database\\MySqlConnection->select(''select `id`, `...', 'array (0 => 13,...', 'TRUE')
#7 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2402): Illuminate\\Database\\Query\\Builder->runSelect()
#8 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2936): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php:2401-2403}()
#9 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2403): Illuminate\\Database\\Query\\Builder->onceWithColumns('array (0 => '*'...', 'class Closure {...')
#10 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(625): Illuminate\\Database\\Query\\Builder->get('array (0 => '*'...')
#11 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(609): App\\Api\\Util\\Query\\QueryBuilder->getModels('array (0 => '*'...')
#12 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasMany.php(17): App\\Api\\Util\\Query\\QueryBuilder->get('???')
#13 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(553): Illuminate\\Database\\Eloquent\\Relations\\HasMany->getResults()
#14 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(491): App\\Management\\Models\\Department->getRelationshipFromMethod(''children1'')
#15 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(440): App\\Management\\Models\\Department->getRelationValue(''children1'')
#16 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2029): App\\Management\\Models\\Department->getAttribute(''children1'')
#17 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(404): App\\Management\\Models\\Department->__get(''children1'')
#18 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(404): App\\Management\\Util\\User::getLeaderDep('class Illuminat...', 'array (1 => arr...', '0')
#19 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(404): App\\Management\\Util\\User::getLeaderDep('class Illuminat...', 'array (1 => arr...', '0')
#20 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(404): App\\Management\\Util\\User::getLeaderDep('class Illuminat...', 'array (1 => arr...', '0')
#21 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(154): App\\Management\\Util\\User::getLeaderDep('class Illuminat...', 'array (1 => arr...', '0')
#22 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(50): App\\Management\\Util\\User::create('array ('usernam...')
#23 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Controllers\\OpenApiController.php(300): App\\Management\\Util\\User::login(''TanHongKun'', ''46479703501cb5...')
#24 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Management\\Controllers\\OpenApiController->getToken('class Illuminat...')
#25 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): App\\Management\\Controllers\\OpenApiController->callAction(''getToken'', 'array (0 => cla...')
#26 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch('class Illuminat...', 'class App\\\\Manag...', ''getToken'')
#27 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#28 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#29 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:719-723}('class Illuminat...')
#30 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:126-132}('class Illuminat...')
#31 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle('class Illuminat...', 'class Closure {...')
#32 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(127): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#33 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(63): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest('class Illuminat...', 'class Closure {...', 'array (0 => cla...')
#34 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle('class Illuminat...', 'class Closure {...', ''100000'', ''1'', '???')
#35 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#36 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Routing\\Pipeline->then('class Closure {...')
#37 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack('class Illuminat...', 'class Illuminat...')
#38 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute('class Illuminat...', 'class Illuminat...')
#39 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute('class Illuminat...')
#40 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch('class Illuminat...')
#41 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): App\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php:164-168}('class Illuminat...')
#42 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:126-132}('class Illuminat...')
#43 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): App\\Http\\Middleware\\TrimStrings->handle('class Illuminat...', 'class Closure {...')
#44 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\TrimStrings->handle('class Illuminat...', 'class Closure {...')
#45 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#46 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle('class Illuminat...', 'class Closure {...')
#47 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#48 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle('class Illuminat...', 'class Closure {...')
#49 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(52): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#50 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle('class Illuminat...', 'class Closure {...')
#51 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#52 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\TrustProxies->handle('class Illuminat...', 'class Closure {...')
#53 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#54 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Routing\\Pipeline->then('class Closure {...')
#55 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): App\\Http\\Kernel->sendRequestThroughRouter('class Illuminat...')
#56 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\public\\index.php(52): App\\Http\\Kernel->handle('class Illuminat...')
#57 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\server.php(21): ()
#58 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\server.php(0): {main}()
#59 {main}
"} 
[2025-08-01 15:14:10] local.ERROR: PHP Parse error: Syntax error, unexpected T_OBJECT_OPERATOR on line 1 {"exception":"[object] (Psy\\Exception\\ParseErrorException(code: 0): PHP Parse error: Syntax error, unexpected T_OBJECT_OPERATOR on line 1 at C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\psy\\psysh\\src\\Exception\\ParseErrorException.php:44)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\psy\\psysh\\src\\CodeCleaner.php(332): Psy\\Exception\\ParseErrorException::fromParseError(Object(PhpParser\\Error))
#1 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\psy\\psysh\\src\\CodeCleaner.php(261): Psy\\CodeCleaner->parse('<?php echo 'Dat...', false)
#2 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\psy\\psysh\\src\\Shell.php(848): Psy\\CodeCleaner->clean(Array, false)
#3 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\psy\\psysh\\src\\Shell.php(877): Psy\\Shell->addCode('echo 'Database ...', true)
#4 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\psy\\psysh\\src\\Shell.php(1342): Psy\\Shell->setCode('echo 'Database ...', true)
#5 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\tinker\\src\\Console\\TinkerCommand.php(76): Psy\\Shell->execute('echo 'Database ...')
#6 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Tinker\\Console\\TinkerCommand->handle()
#7 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#9 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#10 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#11 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(136): Illuminate\\Container\\Container->call(Array)
#12 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\symfony\\console\\Command\\Command.php(298): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(121): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\symfony\\console\\Application.php(1040): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand(Object(Laravel\\Tinker\\Console\\TinkerCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 {main}
"} 
[2025-08-01 15:14:18] local.ERROR: Command "db:show" is not defined.

Did you mean one of these?
    db
    db:seed
    db:wipe {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"db:show\" is not defined.

Did you mean one of these?
    db
    db:seed
    db:wipe at C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\symfony\\console\\Application.php:720)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\symfony\\console\\Application.php(259): Symfony\\Component\\Console\\Application->find('db:show')
#1 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 {main}
"} 
[2025-08-01 15:35:30] local.ERROR: Maximum execution time of 60 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 60 seconds exceeded at C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:368)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Maximum execut...', '0', 'array ('type' =...', '0', '???', '???')
#1 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('array ('type' =...', '0')
#2 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(705): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:359-376}(''select `id`, `...', 'array ()')
#4 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\MySqlConnection->runQueryCallback(''select `id`, `...', 'array ()', 'class Closure {...')
#5 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(376): Illuminate\\Database\\MySqlConnection->run(''select `id`, `...', 'array ()', 'class Closure {...')
#6 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2414): Illuminate\\Database\\MySqlConnection->select(''select `id`, `...', 'array ()', 'TRUE')
#7 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2402): Illuminate\\Database\\Query\\Builder->runSelect()
#8 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2936): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php:2401-2403}()
#9 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2403): Illuminate\\Database\\Query\\Builder->onceWithColumns('array (0 => '*'...', 'class Closure {...')
#10 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(625): Illuminate\\Database\\Query\\Builder->get('array (0 => '*'...')
#11 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(609): App\\Api\\Util\\Query\\QueryBuilder->getModels('array (0 => '*'...')
#12 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(195): App\\Api\\Util\\Query\\QueryBuilder->get('array (0 => '*'...')
#13 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(160): Illuminate\\Database\\Eloquent\\Relations\\HasMany->get('???')
#14 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(673): Illuminate\\Database\\Eloquent\\Relations\\HasMany->getEager()
#15 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(642): App\\Api\\Util\\Query\\QueryBuilder->eagerLoadRelation('array (0 => cla...', ''children'', 'class Closure {...')
#16 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(610): App\\Api\\Util\\Query\\QueryBuilder->eagerLoadRelations('array (0 => cla...')
#17 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(195): App\\Api\\Util\\Query\\QueryBuilder->get('array (0 => '*'...')
#18 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(160): Illuminate\\Database\\Eloquent\\Relations\\HasMany->get('???')
#19 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(673): Illuminate\\Database\\Eloquent\\Relations\\HasMany->getEager()
#20 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(642): App\\Api\\Util\\Query\\QueryBuilder->eagerLoadRelation('array (0 => cla...', ''children'', 'class Closure {...')
#21 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(610): App\\Api\\Util\\Query\\QueryBuilder->eagerLoadRelations('array (0 => cla...')
#22 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasMany.php(17): App\\Api\\Util\\Query\\QueryBuilder->get('???')
#23 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(553): Illuminate\\Database\\Eloquent\\Relations\\HasMany->getResults()
#24 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(491): App\\Management\\Models\\Department->getRelationshipFromMethod(''children1'')
#25 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(440): App\\Management\\Models\\Department->getRelationValue(''children1'')
#26 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2029): App\\Management\\Models\\Department->getAttribute(''children1'')
#27 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(404): App\\Management\\Models\\Department->__get(''children1'')
#28 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(404): App\\Management\\Util\\User::getLeaderDep('class Illuminat...', 'array (1 => arr...', '0')
#29 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(404): App\\Management\\Util\\User::getLeaderDep('class Illuminat...', 'array (1 => arr...', '0')
#30 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(404): App\\Management\\Util\\User::getLeaderDep('class Illuminat...', 'array (1 => arr...', '0')
#31 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(154): App\\Management\\Util\\User::getLeaderDep('class Illuminat...', 'array (1 => arr...', '0')
#32 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(50): App\\Management\\Util\\User::create('array ('usernam...')
#33 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Controllers\\OpenApiController.php(298): App\\Management\\Util\\User::login(''TanHongKun'', ''46479703501cb5...')
#34 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Management\\Controllers\\OpenApiController->getToken('class Illuminat...')
#35 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): App\\Management\\Controllers\\OpenApiController->callAction(''getToken'', 'array (0 => cla...')
#36 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch('class Illuminat...', 'class App\\\\Manag...', ''getToken'')
#37 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#38 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#39 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:719-723}('class Illuminat...')
#40 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:126-132}('class Illuminat...')
#41 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle('class Illuminat...', 'class Closure {...')
#42 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(127): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#43 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(63): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest('class Illuminat...', 'class Closure {...', 'array (0 => cla...')
#44 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle('class Illuminat...', 'class Closure {...', ''100000'', ''1'', '???')
#45 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#46 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Routing\\Pipeline->then('class Closure {...')
#47 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack('class Illuminat...', 'class Illuminat...')
#48 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute('class Illuminat...', 'class Illuminat...')
#49 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute('class Illuminat...')
#50 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch('class Illuminat...')
#51 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): App\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php:164-168}('class Illuminat...')
#52 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:126-132}('class Illuminat...')
#53 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): App\\Http\\Middleware\\TrimStrings->handle('class Illuminat...', 'class Closure {...')
#54 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\TrimStrings->handle('class Illuminat...', 'class Closure {...')
#55 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#56 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle('class Illuminat...', 'class Closure {...')
#57 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#58 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle('class Illuminat...', 'class Closure {...')
#59 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(52): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#60 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle('class Illuminat...', 'class Closure {...')
#61 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#62 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\TrustProxies->handle('class Illuminat...', 'class Closure {...')
#63 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#64 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Routing\\Pipeline->then('class Closure {...')
#65 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): App\\Http\\Kernel->sendRequestThroughRouter('class Illuminat...')
#66 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\public\\index.php(52): App\\Http\\Kernel->handle('class Illuminat...')
#67 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\server.php(21): ()
#68 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\server.php(0): {main}()
#69 {main}
"} 
[2025-08-01 15:39:59] local.ERROR: Target class [web] does not exist. {"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [web] does not exist. at C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:879)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build('web')
#1 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('web', Array, true)
#2 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('web', Array)
#3 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('web', Array)
#4 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(156): Illuminate\\Foundation\\Application->make('web')
#5 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#6 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#7 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#9 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#10 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\server.php(21): require_once('C:\\\\Users\\\\<USER>\\\\Des...')
#28 {main}

[previous exception] [object] (ReflectionException(code: -1): Class web does not exist at C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:877)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(877): ReflectionClass->__construct('web')
#1 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build('web')
#2 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('web', Array, true)
#3 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('web', Array)
#4 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('web', Array)
#5 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(156): Illuminate\\Foundation\\Application->make('web')
#6 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#8 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#10 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\server.php(21): require_once('C:\\\\Users\\\\<USER>\\\\Des...')
#29 {main}
"} 
[2025-08-01 15:39:59] local.ERROR: Target class [web] does not exist. {"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [web] does not exist. at C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:879)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build('web')
#1 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('web', Array, true)
#2 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('web', Array)
#3 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('web', Array)
#4 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(206): Illuminate\\Foundation\\Application->make('web')
#5 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(180): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#6 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\public\\index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#7 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\server.php(21): require_once('C:\\\\Users\\\\<USER>\\\\Des...')
#8 {main}

[previous exception] [object] (ReflectionException(code: -1): Class web does not exist at C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:877)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(877): ReflectionClass->__construct('web')
#1 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build('web')
#2 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('web', Array, true)
#3 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('web', Array)
#4 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('web', Array)
#5 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(206): Illuminate\\Foundation\\Application->make('web')
#6 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(180): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#7 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\public\\index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#8 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\server.php(21): require_once('C:\\\\Users\\\\<USER>\\\\Des...')
#9 {main}
"} 
[2025-08-01 15:41:15] local.ERROR: Maximum execution time of 60 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 60 seconds exceeded at C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:375)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Maximum execut...', '0', 'array ('type' =...', '0', '???', '???')
#1 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('array ('type' =...', '0')
#2 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(705): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:359-376}(''select `id`, `...', 'array ()')
#4 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\MySqlConnection->runQueryCallback(''select `id`, `...', 'array ()', 'class Closure {...')
#5 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(376): Illuminate\\Database\\MySqlConnection->run(''select `id`, `...', 'array ()', 'class Closure {...')
#6 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2414): Illuminate\\Database\\MySqlConnection->select(''select `id`, `...', 'array ()', 'TRUE')
#7 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2402): Illuminate\\Database\\Query\\Builder->runSelect()
#8 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2936): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php:2401-2403}()
#9 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2403): Illuminate\\Database\\Query\\Builder->onceWithColumns('array (0 => '*'...', 'class Closure {...')
#10 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(625): Illuminate\\Database\\Query\\Builder->get('array (0 => '*'...')
#11 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(609): App\\Api\\Util\\Query\\QueryBuilder->getModels('array (0 => '*'...')
#12 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(195): App\\Api\\Util\\Query\\QueryBuilder->get('array (0 => '*'...')
#13 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(160): Illuminate\\Database\\Eloquent\\Relations\\HasMany->get('???')
#14 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(673): Illuminate\\Database\\Eloquent\\Relations\\HasMany->getEager()
#15 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(642): App\\Api\\Util\\Query\\QueryBuilder->eagerLoadRelation('array (0 => cla...', ''children'', 'class Closure {...')
#16 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(610): App\\Api\\Util\\Query\\QueryBuilder->eagerLoadRelations('array (0 => cla...')
#17 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(195): App\\Api\\Util\\Query\\QueryBuilder->get('array (0 => '*'...')
#18 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(160): Illuminate\\Database\\Eloquent\\Relations\\HasMany->get('???')
#19 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(673): Illuminate\\Database\\Eloquent\\Relations\\HasMany->getEager()
#20 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(642): App\\Api\\Util\\Query\\QueryBuilder->eagerLoadRelation('array (0 => cla...', ''children'', 'class Closure {...')
#21 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(610): App\\Api\\Util\\Query\\QueryBuilder->eagerLoadRelations('array (0 => cla...')
#22 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasMany.php(17): App\\Api\\Util\\Query\\QueryBuilder->get('???')
#23 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(553): Illuminate\\Database\\Eloquent\\Relations\\HasMany->getResults()
#24 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(491): App\\Management\\Models\\Department->getRelationshipFromMethod(''children1'')
#25 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(440): App\\Management\\Models\\Department->getRelationValue(''children1'')
#26 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2029): App\\Management\\Models\\Department->getAttribute(''children1'')
#27 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(404): App\\Management\\Models\\Department->__get(''children1'')
#28 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(404): App\\Management\\Util\\User::getLeaderDep('class Illuminat...', 'array (1 => arr...', '0')
#29 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(404): App\\Management\\Util\\User::getLeaderDep('class Illuminat...', 'array (1 => arr...', '0')
#30 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(404): App\\Management\\Util\\User::getLeaderDep('class Illuminat...', 'array (1 => arr...', '0')
#31 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(154): App\\Management\\Util\\User::getLeaderDep('class Illuminat...', 'array (1 => arr...', '0')
#32 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(50): App\\Management\\Util\\User::create('array ('usernam...')
#33 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Controllers\\OpenApiController.php(298): App\\Management\\Util\\User::login(''TanHongKun'', ''46479703501cb5...')
#34 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Management\\Controllers\\OpenApiController->getToken('class Illuminat...')
#35 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): App\\Management\\Controllers\\OpenApiController->callAction(''getToken'', 'array (0 => cla...')
#36 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch('class Illuminat...', 'class App\\\\Manag...', ''getToken'')
#37 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#38 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#39 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:719-723}('class Illuminat...')
#40 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:126-132}('class Illuminat...')
#41 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle('class Illuminat...', 'class Closure {...')
#42 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(127): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#43 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(63): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest('class Illuminat...', 'class Closure {...', 'array (0 => cla...')
#44 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle('class Illuminat...', 'class Closure {...', ''100000'', ''1'', '???')
#45 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#46 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Routing\\Pipeline->then('class Closure {...')
#47 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack('class Illuminat...', 'class Illuminat...')
#48 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute('class Illuminat...', 'class Illuminat...')
#49 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute('class Illuminat...')
#50 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch('class Illuminat...')
#51 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): App\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php:164-168}('class Illuminat...')
#52 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:126-132}('class Illuminat...')
#53 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): App\\Http\\Middleware\\TrimStrings->handle('class Illuminat...', 'class Closure {...')
#54 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\TrimStrings->handle('class Illuminat...', 'class Closure {...')
#55 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#56 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle('class Illuminat...', 'class Closure {...')
#57 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#58 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle('class Illuminat...', 'class Closure {...')
#59 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(52): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#60 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle('class Illuminat...', 'class Closure {...')
#61 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#62 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\TrustProxies->handle('class Illuminat...', 'class Closure {...')
#63 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#64 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Routing\\Pipeline->then('class Closure {...')
#65 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): App\\Http\\Kernel->sendRequestThroughRouter('class Illuminat...')
#66 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\public\\index.php(52): App\\Http\\Kernel->handle('class Illuminat...')
#67 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\server.php(21): ()
#68 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\server.php(0): {main}()
#69 {main}
"} 
[2025-08-01 15:47:33] local.ERROR: Maximum execution time of 60 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 60 seconds exceeded at C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:375)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Maximum execut...', '0', 'array ('type' =...', '0', '???', '???')
#1 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('array ('type' =...', '0')
#2 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(705): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:359-376}(''select `id`, `...', 'array (0 => 310...')
#4 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\MySqlConnection->runQueryCallback(''select `id`, `...', 'array (0 => 310...', 'class Closure {...')
#5 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(376): Illuminate\\Database\\MySqlConnection->run(''select `id`, `...', 'array (0 => 310...', 'class Closure {...')
#6 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2414): Illuminate\\Database\\MySqlConnection->select(''select `id`, `...', 'array (0 => 310...', 'TRUE')
#7 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2402): Illuminate\\Database\\Query\\Builder->runSelect()
#8 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2936): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php:2401-2403}()
#9 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2403): Illuminate\\Database\\Query\\Builder->onceWithColumns('array (0 => '*'...', 'class Closure {...')
#10 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(625): Illuminate\\Database\\Query\\Builder->get('array (0 => '*'...')
#11 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(609): App\\Api\\Util\\Query\\QueryBuilder->getModels('array (0 => '*'...')
#12 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasMany.php(17): App\\Api\\Util\\Query\\QueryBuilder->get('???')
#13 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(553): Illuminate\\Database\\Eloquent\\Relations\\HasMany->getResults()
#14 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(491): App\\Management\\Models\\Department->getRelationshipFromMethod(''children1'')
#15 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(440): App\\Management\\Models\\Department->getRelationValue(''children1'')
#16 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2029): App\\Management\\Models\\Department->getAttribute(''children1'')
#17 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(404): App\\Management\\Models\\Department->__get(''children1'')
#18 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(404): App\\Management\\Util\\User::getLeaderDep('class Illuminat...', 'array (1 => arr...', '0')
#19 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(404): App\\Management\\Util\\User::getLeaderDep('class Illuminat...', 'array (1 => arr...', '0')
#20 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(404): App\\Management\\Util\\User::getLeaderDep('class Illuminat...', 'array (1 => arr...', '0')
#21 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(404): App\\Management\\Util\\User::getLeaderDep('class Illuminat...', 'array (1 => arr...', '0')
#22 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(404): App\\Management\\Util\\User::getLeaderDep('class Illuminat...', 'array (1 => arr...', '0')
#23 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(404): App\\Management\\Util\\User::getLeaderDep('class Illuminat...', 'array (1 => arr...', '0')
#24 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(154): App\\Management\\Util\\User::getLeaderDep('class Illuminat...', 'array (1 => arr...', '0')
#25 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(50): App\\Management\\Util\\User::create('array ('usernam...')
#26 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Controllers\\OpenApiController.php(298): App\\Management\\Util\\User::login(''TanHongKun'', ''46479703501cb5...')
#27 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Management\\Controllers\\OpenApiController->getToken('class Illuminat...')
#28 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): App\\Management\\Controllers\\OpenApiController->callAction(''getToken'', 'array (0 => cla...')
#29 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch('class Illuminat...', 'class App\\\\Manag...', ''getToken'')
#30 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#31 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#32 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:719-723}('class Illuminat...')
#33 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:126-132}('class Illuminat...')
#34 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle('class Illuminat...', 'class Closure {...')
#35 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(127): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#36 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(63): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest('class Illuminat...', 'class Closure {...', 'array (0 => cla...')
#37 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle('class Illuminat...', 'class Closure {...', ''100000'', ''1'', '???')
#38 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#39 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Routing\\Pipeline->then('class Closure {...')
#40 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack('class Illuminat...', 'class Illuminat...')
#41 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute('class Illuminat...', 'class Illuminat...')
#42 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute('class Illuminat...')
#43 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch('class Illuminat...')
#44 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): App\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php:164-168}('class Illuminat...')
#45 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:126-132}('class Illuminat...')
#46 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): App\\Http\\Middleware\\TrimStrings->handle('class Illuminat...', 'class Closure {...')
#47 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\TrimStrings->handle('class Illuminat...', 'class Closure {...')
#48 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#49 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle('class Illuminat...', 'class Closure {...')
#50 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#51 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle('class Illuminat...', 'class Closure {...')
#52 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(52): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#53 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle('class Illuminat...', 'class Closure {...')
#54 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#55 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\TrustProxies->handle('class Illuminat...', 'class Closure {...')
#56 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#57 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Routing\\Pipeline->then('class Closure {...')
#58 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): App\\Http\\Kernel->sendRequestThroughRouter('class Illuminat...')
#59 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\public\\index.php(52): App\\Http\\Kernel->handle('class Illuminat...')
#60 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\server.php(21): ()
#61 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\server.php(0): {main}()
#62 {main}
"} 
[2025-08-01 15:59:55] local.ERROR: Maximum execution time of 60 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 60 seconds exceeded at C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:368)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Maximum execut...', '0', 'array ('type' =...', '0', '???', '???')
#1 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('array ('type' =...', '0')
#2 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(705): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:359-376}(''select `id`, `...', 'array (0 => 298...')
#4 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\MySqlConnection->runQueryCallback(''select `id`, `...', 'array (0 => 298...', 'class Closure {...')
#5 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(376): Illuminate\\Database\\MySqlConnection->run(''select `id`, `...', 'array (0 => 298...', 'class Closure {...')
#6 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2414): Illuminate\\Database\\MySqlConnection->select(''select `id`, `...', 'array (0 => 298...', 'TRUE')
#7 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2402): Illuminate\\Database\\Query\\Builder->runSelect()
#8 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2936): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php:2401-2403}()
#9 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2403): Illuminate\\Database\\Query\\Builder->onceWithColumns('array (0 => '*'...', 'class Closure {...')
#10 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(625): Illuminate\\Database\\Query\\Builder->get('array (0 => '*'...')
#11 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(609): App\\Api\\Util\\Query\\QueryBuilder->getModels('array (0 => '*'...')
#12 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasMany.php(17): App\\Api\\Util\\Query\\QueryBuilder->get('???')
#13 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(553): Illuminate\\Database\\Eloquent\\Relations\\HasMany->getResults()
#14 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(491): App\\Management\\Models\\Department->getRelationshipFromMethod(''children1'')
#15 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(440): App\\Management\\Models\\Department->getRelationValue(''children1'')
#16 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2029): App\\Management\\Models\\Department->getAttribute(''children1'')
#17 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(404): App\\Management\\Models\\Department->__get(''children1'')
#18 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(404): App\\Management\\Util\\User::getLeaderDep('class Illuminat...', 'array (1 => arr...', '0')
#19 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(404): App\\Management\\Util\\User::getLeaderDep('class Illuminat...', 'array (1 => arr...', '0')
#20 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(404): App\\Management\\Util\\User::getLeaderDep('class Illuminat...', 'array (1 => arr...', '0')
#21 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(404): App\\Management\\Util\\User::getLeaderDep('class Illuminat...', 'array (1 => arr...', '0')
#22 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(404): App\\Management\\Util\\User::getLeaderDep('class Illuminat...', 'array (1 => arr...', '0')
#23 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(154): App\\Management\\Util\\User::getLeaderDep('class Illuminat...', 'array (1 => arr...', '0')
#24 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(50): App\\Management\\Util\\User::create('array ('usernam...')
#25 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Controllers\\OpenApiController.php(301): App\\Management\\Util\\User::login(''TanHongKun'', ''46479703501cb5...')
#26 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Management\\Controllers\\OpenApiController->getToken('class Illuminat...')
#27 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): App\\Management\\Controllers\\OpenApiController->callAction(''getToken'', 'array (0 => cla...')
#28 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch('class Illuminat...', 'class App\\\\Manag...', ''getToken'')
#29 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#30 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#31 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:719-723}('class Illuminat...')
#32 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:126-132}('class Illuminat...')
#33 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle('class Illuminat...', 'class Closure {...')
#34 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(127): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#35 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(63): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest('class Illuminat...', 'class Closure {...', 'array (0 => cla...')
#36 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle('class Illuminat...', 'class Closure {...', ''100000'', ''1'', '???')
#37 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#38 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Routing\\Pipeline->then('class Closure {...')
#39 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack('class Illuminat...', 'class Illuminat...')
#40 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute('class Illuminat...', 'class Illuminat...')
#41 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute('class Illuminat...')
#42 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch('class Illuminat...')
#43 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): App\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php:164-168}('class Illuminat...')
#44 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:126-132}('class Illuminat...')
#45 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): App\\Http\\Middleware\\TrimStrings->handle('class Illuminat...', 'class Closure {...')
#46 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\TrimStrings->handle('class Illuminat...', 'class Closure {...')
#47 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#48 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle('class Illuminat...', 'class Closure {...')
#49 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#50 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle('class Illuminat...', 'class Closure {...')
#51 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(52): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#52 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle('class Illuminat...', 'class Closure {...')
#53 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#54 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\TrustProxies->handle('class Illuminat...', 'class Closure {...')
#55 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#56 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Routing\\Pipeline->then('class Closure {...')
#57 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): App\\Http\\Kernel->sendRequestThroughRouter('class Illuminat...')
#58 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\public\\index.php(52): App\\Http\\Kernel->handle('class Illuminat...')
#59 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\server.php(21): ()
#60 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\server.php(0): {main}()
#61 {main}
"} 
[2025-08-01 16:03:28] local.ERROR: Maximum execution time of 60 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 60 seconds exceeded at C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:368)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Maximum execut...', '0', 'array ('type' =...', '0', '???', '???')
#1 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('array ('type' =...', '0')
#2 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(705): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:359-376}(''select `id`, `...', 'array (0 => 313...')
#4 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\MySqlConnection->runQueryCallback(''select `id`, `...', 'array (0 => 313...', 'class Closure {...')
#5 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(376): Illuminate\\Database\\MySqlConnection->run(''select `id`, `...', 'array (0 => 313...', 'class Closure {...')
#6 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2414): Illuminate\\Database\\MySqlConnection->select(''select `id`, `...', 'array (0 => 313...', 'TRUE')
#7 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2402): Illuminate\\Database\\Query\\Builder->runSelect()
#8 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2936): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php:2401-2403}()
#9 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2403): Illuminate\\Database\\Query\\Builder->onceWithColumns('array (0 => '*'...', 'class Closure {...')
#10 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(625): Illuminate\\Database\\Query\\Builder->get('array (0 => '*'...')
#11 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(609): App\\Api\\Util\\Query\\QueryBuilder->getModels('array (0 => '*'...')
#12 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasMany.php(17): App\\Api\\Util\\Query\\QueryBuilder->get('???')
#13 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(553): Illuminate\\Database\\Eloquent\\Relations\\HasMany->getResults()
#14 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(491): App\\Management\\Models\\Department->getRelationshipFromMethod(''children1'')
#15 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(440): App\\Management\\Models\\Department->getRelationValue(''children1'')
#16 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2029): App\\Management\\Models\\Department->getAttribute(''children1'')
#17 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(404): App\\Management\\Models\\Department->__get(''children1'')
#18 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(404): App\\Management\\Util\\User::getLeaderDep('class Illuminat...', 'array (1 => arr...', '0')
#19 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(404): App\\Management\\Util\\User::getLeaderDep('class Illuminat...', 'array (1 => arr...', '0')
#20 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(404): App\\Management\\Util\\User::getLeaderDep('class Illuminat...', 'array (1 => arr...', '0')
#21 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(404): App\\Management\\Util\\User::getLeaderDep('class Illuminat...', 'array (1 => arr...', '0')
#22 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(404): App\\Management\\Util\\User::getLeaderDep('class Illuminat...', 'array (1 => arr...', '0')
#23 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(154): App\\Management\\Util\\User::getLeaderDep('class Illuminat...', 'array (1 => arr...', '0')
#24 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(50): App\\Management\\Util\\User::create('array ('usernam...')
#25 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Controllers\\OpenApiController.php(301): App\\Management\\Util\\User::login(''TanHongKun'', ''46479703501cb5...')
#26 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Management\\Controllers\\OpenApiController->getToken('class Illuminat...')
#27 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): App\\Management\\Controllers\\OpenApiController->callAction(''getToken'', 'array (0 => cla...')
#28 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch('class Illuminat...', 'class App\\\\Manag...', ''getToken'')
#29 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#30 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#31 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:719-723}('class Illuminat...')
#32 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:126-132}('class Illuminat...')
#33 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle('class Illuminat...', 'class Closure {...')
#34 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(127): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#35 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(63): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest('class Illuminat...', 'class Closure {...', 'array (0 => cla...')
#36 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle('class Illuminat...', 'class Closure {...', ''100000'', ''1'', '???')
#37 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#38 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Routing\\Pipeline->then('class Closure {...')
#39 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack('class Illuminat...', 'class Illuminat...')
#40 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute('class Illuminat...', 'class Illuminat...')
#41 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute('class Illuminat...')
#42 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch('class Illuminat...')
#43 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): App\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php:164-168}('class Illuminat...')
#44 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:126-132}('class Illuminat...')
#45 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): App\\Http\\Middleware\\TrimStrings->handle('class Illuminat...', 'class Closure {...')
#46 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\TrimStrings->handle('class Illuminat...', 'class Closure {...')
#47 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#48 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle('class Illuminat...', 'class Closure {...')
#49 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#50 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle('class Illuminat...', 'class Closure {...')
#51 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(52): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#52 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle('class Illuminat...', 'class Closure {...')
#53 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#54 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\TrustProxies->handle('class Illuminat...', 'class Closure {...')
#55 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#56 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Routing\\Pipeline->then('class Closure {...')
#57 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): App\\Http\\Kernel->sendRequestThroughRouter('class Illuminat...')
#58 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\public\\index.php(52): App\\Http\\Kernel->handle('class Illuminat...')
#59 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\server.php(21): ()
#60 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\server.php(0): {main}()
#61 {main}
"} 
[2025-08-01 16:04:59] local.ERROR: Maximum execution time of 60 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 60 seconds exceeded at C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:368)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(222): Symfony\\Component\\ErrorHandler\\Error\\FatalError->__construct(''Maximum execut...', '0', 'array ('type' =...', '0', '???', '???')
#1 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->fatalErrorFromPhpError('array ('type' =...', '0')
#2 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(0): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleShutdown()
#3 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(705): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:359-376}(''select `id`, `...', 'array ()')
#4 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\MySqlConnection->runQueryCallback(''select `id`, `...', 'array ()', 'class Closure {...')
#5 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(376): Illuminate\\Database\\MySqlConnection->run(''select `id`, `...', 'array ()', 'class Closure {...')
#6 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2414): Illuminate\\Database\\MySqlConnection->select(''select `id`, `...', 'array ()', 'TRUE')
#7 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2402): Illuminate\\Database\\Query\\Builder->runSelect()
#8 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2936): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php:2401-2403}()
#9 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2403): Illuminate\\Database\\Query\\Builder->onceWithColumns('array (0 => '*'...', 'class Closure {...')
#10 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(625): Illuminate\\Database\\Query\\Builder->get('array (0 => '*'...')
#11 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(609): App\\Api\\Util\\Query\\QueryBuilder->getModels('array (0 => '*'...')
#12 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(195): App\\Api\\Util\\Query\\QueryBuilder->get('array (0 => '*'...')
#13 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(160): Illuminate\\Database\\Eloquent\\Relations\\HasMany->get('???')
#14 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(673): Illuminate\\Database\\Eloquent\\Relations\\HasMany->getEager()
#15 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(642): App\\Api\\Util\\Query\\QueryBuilder->eagerLoadRelation('array (0 => cla...', ''children'', 'class Closure {...')
#16 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(610): App\\Api\\Util\\Query\\QueryBuilder->eagerLoadRelations('array (0 => cla...')
#17 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(195): App\\Api\\Util\\Query\\QueryBuilder->get('array (0 => '*'...')
#18 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(160): Illuminate\\Database\\Eloquent\\Relations\\HasMany->get('???')
#19 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(673): Illuminate\\Database\\Eloquent\\Relations\\HasMany->getEager()
#20 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(642): App\\Api\\Util\\Query\\QueryBuilder->eagerLoadRelation('array (0 => cla...', ''children'', 'class Closure {...')
#21 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(610): App\\Api\\Util\\Query\\QueryBuilder->eagerLoadRelations('array (0 => cla...')
#22 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasMany.php(17): App\\Api\\Util\\Query\\QueryBuilder->get('???')
#23 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(553): Illuminate\\Database\\Eloquent\\Relations\\HasMany->getResults()
#24 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(491): App\\Management\\Models\\Department->getRelationshipFromMethod(''children1'')
#25 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(440): App\\Management\\Models\\Department->getRelationValue(''children1'')
#26 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2029): App\\Management\\Models\\Department->getAttribute(''children1'')
#27 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(404): App\\Management\\Models\\Department->__get(''children1'')
#28 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(404): App\\Management\\Util\\User::getLeaderDep('class Illuminat...', 'array (1 => arr...', '0')
#29 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(404): App\\Management\\Util\\User::getLeaderDep('class Illuminat...', 'array (1 => arr...', '0')
#30 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(404): App\\Management\\Util\\User::getLeaderDep('class Illuminat...', 'array (1 => arr...', '0')
#31 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(154): App\\Management\\Util\\User::getLeaderDep('class Illuminat...', 'array (1 => arr...', '0')
#32 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Util\\User.php(50): App\\Management\\Util\\User::create('array ('usernam...')
#33 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\app\\Management\\Controllers\\OpenApiController.php(301): App\\Management\\Util\\User::login(''TanHongKun'', ''46479703501cb5...')
#34 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Management\\Controllers\\OpenApiController->getToken('class Illuminat...')
#35 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): App\\Management\\Controllers\\OpenApiController->callAction(''getToken'', 'array (0 => cla...')
#36 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch('class Illuminat...', 'class App\\\\Manag...', ''getToken'')
#37 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#38 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#39 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:719-723}('class Illuminat...')
#40 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:126-132}('class Illuminat...')
#41 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle('class Illuminat...', 'class Closure {...')
#42 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(127): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#43 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(63): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest('class Illuminat...', 'class Closure {...', 'array (0 => cla...')
#44 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle('class Illuminat...', 'class Closure {...', ''100000'', ''1'', '???')
#45 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#46 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Routing\\Pipeline->then('class Closure {...')
#47 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack('class Illuminat...', 'class Illuminat...')
#48 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute('class Illuminat...', 'class Illuminat...')
#49 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute('class Illuminat...')
#50 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch('class Illuminat...')
#51 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): App\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php:164-168}('class Illuminat...')
#52 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:126-132}('class Illuminat...')
#53 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): App\\Http\\Middleware\\TrimStrings->handle('class Illuminat...', 'class Closure {...')
#54 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\TrimStrings->handle('class Illuminat...', 'class Closure {...')
#55 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#56 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle('class Illuminat...', 'class Closure {...')
#57 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#58 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle('class Illuminat...', 'class Closure {...')
#59 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(52): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#60 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle('class Illuminat...', 'class Closure {...')
#61 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#62 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\TrustProxies->handle('class Illuminat...', 'class Closure {...')
#63 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Routing\\Pipeline->Illuminate\\Pipeline\\{closure:C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:143-174}('class Illuminat...')
#64 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Routing\\Pipeline->then('class Closure {...')
#65 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): App\\Http\\Kernel->sendRequestThroughRouter('class Illuminat...')
#66 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\public\\index.php(52): App\\Http\\Kernel->handle('class Illuminat...')
#67 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\server.php(21): ()
#68 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\server.php(0): {main}()
#69 {main}
"} 
[2025-08-01 16:19:49] local.ERROR: Target class [web] does not exist. {"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [web] does not exist. at C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:879)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build('web')
#1 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('web', Array, true)
#2 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('web', Array)
#3 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('web', Array)
#4 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(156): Illuminate\\Foundation\\Application->make('web')
#5 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#6 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#7 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#9 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#10 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\server.php(21): require_once('C:\\\\Users\\\\<USER>\\\\Des...')
#28 {main}

[previous exception] [object] (ReflectionException(code: -1): Class web does not exist at C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:877)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(877): ReflectionClass->__construct('web')
#1 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build('web')
#2 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('web', Array, true)
#3 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('web', Array)
#4 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('web', Array)
#5 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(156): Illuminate\\Foundation\\Application->make('web')
#6 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#8 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#10 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\server.php(21): require_once('C:\\\\Users\\\\<USER>\\\\Des...')
#29 {main}
"} 
[2025-08-01 16:19:49] local.ERROR: Target class [web] does not exist. {"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [web] does not exist. at C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:879)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build('web')
#1 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('web', Array, true)
#2 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('web', Array)
#3 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('web', Array)
#4 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(206): Illuminate\\Foundation\\Application->make('web')
#5 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(180): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#6 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\public\\index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#7 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\server.php(21): require_once('C:\\\\Users\\\\<USER>\\\\Des...')
#8 {main}

[previous exception] [object] (ReflectionException(code: -1): Class web does not exist at C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:877)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(877): ReflectionClass->__construct('web')
#1 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build('web')
#2 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('web', Array, true)
#3 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('web', Array)
#4 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('web', Array)
#5 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(206): Illuminate\\Foundation\\Application->make('web')
#6 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(180): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#7 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\public\\index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#8 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\server.php(21): require_once('C:\\\\Users\\\\<USER>\\\\Des...')
#9 {main}
"} 
[2025-08-01 16:35:42] local.ERROR: Target class [web] does not exist. {"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [web] does not exist. at C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:879)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build('web')
#1 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('web', Array, true)
#2 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('web', Array)
#3 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('web', Array)
#4 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(156): Illuminate\\Foundation\\Application->make('web')
#5 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#6 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#7 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#9 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#10 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\server.php(21): require_once('C:\\\\Users\\\\<USER>\\\\Des...')
#28 {main}

[previous exception] [object] (ReflectionException(code: -1): Class web does not exist at C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:877)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(877): ReflectionClass->__construct('web')
#1 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build('web')
#2 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('web', Array, true)
#3 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('web', Array)
#4 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('web', Array)
#5 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(156): Illuminate\\Foundation\\Application->make('web')
#6 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#8 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#10 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\server.php(21): require_once('C:\\\\Users\\\\<USER>\\\\Des...')
#29 {main}
"} 
[2025-08-01 16:35:43] local.ERROR: Target class [web] does not exist. {"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [web] does not exist. at C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:879)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build('web')
#1 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('web', Array, true)
#2 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('web', Array)
#3 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('web', Array)
#4 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(206): Illuminate\\Foundation\\Application->make('web')
#5 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(180): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#6 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\public\\index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#7 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\server.php(21): require_once('C:\\\\Users\\\\<USER>\\\\Des...')
#8 {main}

[previous exception] [object] (ReflectionException(code: -1): Class web does not exist at C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:877)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(877): ReflectionClass->__construct('web')
#1 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build('web')
#2 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('web', Array, true)
#3 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('web', Array)
#4 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('web', Array)
#5 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(206): Illuminate\\Foundation\\Application->make('web')
#6 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(180): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#7 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\public\\index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#8 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\server.php(21): require_once('C:\\\\Users\\\\<USER>\\\\Des...')
#9 {main}
"} 
[2025-08-01 16:35:45] local.ERROR: Target class [web] does not exist. {"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [web] does not exist. at C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:879)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build('web')
#1 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('web', Array, true)
#2 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('web', Array)
#3 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('web', Array)
#4 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(156): Illuminate\\Foundation\\Application->make('web')
#5 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#6 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#7 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#9 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#10 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\server.php(21): require_once('C:\\\\Users\\\\<USER>\\\\Des...')
#28 {main}

[previous exception] [object] (ReflectionException(code: -1): Class web does not exist at C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:877)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(877): ReflectionClass->__construct('web')
#1 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build('web')
#2 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('web', Array, true)
#3 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('web', Array)
#4 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('web', Array)
#5 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(156): Illuminate\\Foundation\\Application->make('web')
#6 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#8 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#10 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\ouath\\auth\\auth\\auth\\api\\server.php(21): require_once('C:\\\\Users\\\\<USER>\\\\Des...')
#29 {main}
"} 
