<?php

namespace App\Api\Util\Query;

class QueryBuilder extends \Illuminate\Database\Eloquent\Builder
{
    /**
     * 用于修饰再传入setWheres的参数处理
     * @var callable
     */
    protected $whereHandler;

    /**
     * 在setWheres时会用于触发
     * @param  callable  $callback
     * @return void
     */
    public function setWhereHandler(callable $handler)
    {
        $this->whereHandler = $handler;
    }

    /**
     * 返回当前设置的model的name
     * @return string 
     */
    public function getTable()
    {
        return ($this->model->getTable()) ?? '';
    }

    /**
     * 输入一块where,不同批次输入就是另一个块or
     * @param array $wheres 一个用于laravel查询的wheres
     * @param string $table 数据库名
     */
    public function setWheres($wheres, $table = '')
    {
        //如果没有where就跳过吧
        if (empty($wheres)) return;

        //如果model声明了cookWheres，需要自定义处理搜索项
        //不直接搜索model的方法是为了解耦
        if (!is_null($this->whereHandler)) {
            $wheres = call_user_func($this->whereHand<PERSON>, $wheres);
        }

        //如果没填table名字就搞个默认的咯
        if (empty($table)) $table = $this->getTable();

        $this->query->whereNested(function ($query) use ($table, $wheres) {
            foreach ($wheres as $k => $whereItem) {
                if (!is_numeric($k)) {
                    //key value形式的
                    $value = $whereItem;
                    $op = (is_array($whereItem)) ? 'in' : '=';

                    $whereItem = [$k, $op, $value];
                }

                //普通的数组参数
                $whereItem[0] = $table . '.' . $whereItem[0];

                $op = strtolower($whereItem[1]);
                switch ($op) {
                        //兼容Eloquent没有的写法
                    case 'in':
                        $query->whereIn($whereItem[0], $whereItem[2]);
                        break;
                    case 'notin':
                        $query->whereIn($whereItem[0], $whereItem[2], 'and', true);
                        break;
                    default:
                        $query->where(...array_values($whereItem));
                }
            }
        }, 'or');

        return $this;
    }

    /**
     * 为查询字段前面都加上fieldname
     * @param string $table 数据库名
     * @param array[string] $fields
     */
    protected static function addTableNameForFields($table, &$fields)
    {
        if (empty($table)) return;

        //未解决如果是函数字段的兼容问题
        //未解决一些奇怪写法的兼容问题

        array_walk($fields, function (&$v) use ($table) {
            $v = $table . '.' . $v;
        });
    }

    /**
     * 设置字段
     * @param array $fields 用于laravel查询的field
     * @param string $table 数据库名
     */
    public function setFields($fields, $table = '')
    {
        if (empty($table)) $table = $this->getTable();

        self::addTableNameForFields($table, $fields);

        if (!empty($this->query->columns)) {
            $this->query->columns = array_merge($this->query->columns, $fields);
        } else {
            $this->query->columns = $fields;
        }

        return $this;
    }

    /**
     * @param string $type left|cross|right|inner
     * @param string $table 数据库名
     * @param \Closure $joinHandler 处理join的函数，参考https://laravel.com/docs/8.x/queries#advanced-join-clauses
     * @param array[string] $fields
     */
    public function setJoin($type, $table, $joinHandler, $fields)
    {
        //处理一下数据表名（万一后面接了别名
        $tableName = explode(' ', $table)[0];

        //设置join之后要查询的字段
        $this->setFields($fields, $tableName);

        //由于这里肯定是用了闭包的方式查询，所以帮它优化调用
        //参考 vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php:468
        $joinClause = new \Illuminate\Database\Query\JoinClause(
            $this->query,
            $type,
            $tableName
        );
        call_user_func($joinHandler, $joinClause);
        $this->query->joins[] = $joinClause;
        $this->addBinding($joinClause->getBindings(), 'join');

        return $this;
    }

    /**
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param array $option 
     */
    public function queryOption($option)
    {
        isset($option['limit']) && $this->query->limit($option['limit']);
        isset($option['offset']) && $this->query->offset($option['offset']);
        isset($option['orderBy']) && $this->query->orderByRaw($option['orderBy']);

        return $this;
    }

    /**
     * @param array $values 要插入的数据
     * @param int $chunkSize 分块的体积
     */
    public function chunkInsert($values, $chunkSize = 100)
    {
        if (empty($values)) return 0;

        $i = 1;
        $total = count($values);
        $records = [];
        $success = 0;
        do {
            $records[] = $values[$i - 1];
            if (count($records) > $chunkSize || $i == $total) {
                $result = $this->query->insert($records);
                //添加正确插入的数量
                if ($result) $success += $chunkSize;
                //重置
                $records = [];
            }
            $i++;
        } while ($i <= $total);

        return $success;
    }
}
