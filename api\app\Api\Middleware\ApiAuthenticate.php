<?php

namespace App\Api\Middleware;

use Closure;
use \App\Admin\Services;
use \App\Admin\Models;
use Illuminate\Support\Facades\DB;
use App\Exceptions\ServiceException;
use App\Api\Util\Enum\ErrorCode;
use Illuminate\Support\Facades\Route;
use Illuminate\Http\Request;

class ApiAuthenticate
{
    public function handle(Request $request, Closure $next)
    {
        if (!$request->wantsJson()) {
            throw new ServiceException('当前服务仅支持json response', 10000);
        }

        // Perform action
        $token = $request->header('Authorization');

        if (empty($token)) {
            throw new ServiceException('没发现token', ErrorCode::TOKEN_NOF_FOUND);
        }

        //用token兑换用户信息
        [$userId, $permissionIds] = Services\AccountLoginInfo::createInfoByToken($token);

        //将请求路径信息兑换成一下权限id
        $routeName = Route::currentRouteName();
        if (!is_null($routeName) && preg_match('/^route_([0-9]+)$/', $routeName, $match)) {
            //格式为 "route_" + $permissionId，那么后面就是权限id
            $permissionId = $match[1];
        } else {
            //只好先去查一遍数据库
            //如果实在未找到就返回一个null
            $route = '/' . Route::getCurrentRoute()->uri();
            $permissionId = Services\Permission::getPermissionId($route);
        }

        //如果有对应的权限id，判断是否有权限

        //如果没有对应规则的name，那么证明路由没有归纳到管理范围
        //要么就是接口先于生成权限，然后先被请求过了（开发的时候）
        //要么就是真的没有权限（证明谁都有权限）
        if ($permissionId && !in_array($permissionId, $permissionIds)) {
            throw new ServiceException('没有权限', ErrorCode::PERMISSION_DENIED);
        }

        return $next($request);
    }
}
