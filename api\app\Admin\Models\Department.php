<?php

namespace App\Admin\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\AsArrayObject;

class Department extends Model
{
    use \App\Api\Util\Query\ModelHelper;

    const TABLE_NAME = 'department';

    const STATUS_ENABLE = 1;
    const STATUS_DISABLE = 0;

    protected $table = self::TABLE_NAME;

    protected $fillable = [
        'name', 'pidstr', 'parent_id', 'note', 'status'
    ];

    protected $whereHandler = [self::class, 'cookWheres'];

    public static function cookWheres($filterValue)
    {
        if (isset($filterValue['pidstr'])) {
            $filterValue[] = ['pidstr', 'like', '%_' . $filterValue['pidstr'] . '_%'];
            unset($filterValue['pidstr']);
        }

        if (isset($filterValue['name'])) {
            $filterValue[] = ['name', 'like', '%' . $filterValue['name'] . '%'];
            unset($filterValue['name']);
        }

        return $filterValue;
    }
}
