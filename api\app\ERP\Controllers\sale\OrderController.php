<?php
namespace App\ERP\Controllers\sale;

use App\ERP\Services;
use App\Api\Util\User;
use Illuminate\Http\Request;
use App\Api\Util\ApiResponse;
use Illuminate\Support\Facades\DB;
use App\Api\Util\Enum\ValidationRule;

//销售订单管理
class OrderController extends \App\Api\Controllers\ApiController
{
    public function getPageList(Request $request)
    {
        $req = self::paramsFilter($request->all(), [
            'page', 'pageSize'
        ]);

        $rep = Services\AdminSDK\SaleOrder::getPageList(env('ERPFLAG'), $req, $request->header('Authorization'));
        $rep['nav'] = [];
        $rep['nav']['columns'] = [];
        $rep['nav']['columns'][] = ['field'=>'client_name', 'type'=>'text','title'=>'客户名称'];
        $rep['nav']['columns'][] = ['field'=>'project_name', 'type'=>'text','title'=>'项目名称'];
        $rep['nav']['columns'][] = ['field'=>'department', 'type'=>'text','title'=>'部门'];
        $rep['nav']['columns'][] = ['field'=>'total_price', 'type'=>'number','title'=>'总金额', 'digit'=>2];
        $rep['nav']['columns'][] = ['field'=>'program_incharge_name', 'type'=>'text','title'=>'方案经理'];
        $rep['nav']['columns'][] = ['field'=>'inCharge_name', 'type'=>'text','title'=>'项目经理'];
        $rep['nav']['columns'][] = ['field'=>'delivery_incharge_name', 'type'=>'text','title'=>'交付经理'];
        $rep['nav']['columns'][] = ['field'=>'created_at', 'type'=>'text','title'=>'创建时间'];
        //$rep['nav']['columns'][] = ['field'=>'status', 'type'=>'text','title'=>'状态'];  //0待审核 1已审核 15结束


        return ApiResponse::success($rep);
    }

    public function create(Request $request)
    {
        $req = self::paramsFilter($request->post(), [
            'name', 'icon', 'color', 'process_type_id' ,'type', 'system',
            'department_id', 'sort', 'entity', 'entity_field', 'note', 'status'
        ]);

        return ApiResponse::success([]);
    }

    public function detail(Request $request)
    {
        $req = self::paramsFilter($request->all(), [
            ['id','work_id']
        ]);

        $rep = Services\AdminSDK\SaleOrder::detail(env('ERPFLAG'), $req, $request->header('Authorization'));

        $rep['form'] = [];
        $rep['form']['type'] = [];
        return ApiResponse::success($rep);
    }

    public function delete(Request $request)
    {
        $req = self::paramsFilter($request->post(), [
            'id'
        ]);

        return ApiResponse::success([]);
    }

    public function getClientList(Request $request)
    {
        $sysList = array_keys(\App\Management\Services\AdminSDK\Util::getSystem());

        return ApiResponse::success($sysList);
    }
}
