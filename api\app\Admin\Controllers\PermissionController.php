<?php

namespace App\Admin\Controllers;

use App\Api\Util\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Admin\Models;
use App\Admin\Services;
use App\Api\Util\User;

class PermissionController extends \App\Api\Controllers\ApiController
{
    public function getTree(Request $request)
    {
        //以下会通过权限系统过滤到仅开发者账号可以执行
        //菜单管理时会调用，没有查询条件，去permission表全部查出来

        $req = self::paramsFilter($request->input(), [
            'status', ['isHidden', 'is_hidden'], 'type'
        ]);

        //检查参数
        $validator = new \App\Admin\Validator\Permission();
        $result = $validator->check($req);

        $where = $req;

        //按条件查出permission
        $collection = Services\Permission::getCollection($where, [
            'id', 'title', 'parent_id as parentId', 'name as menuName', 'type',
            'order as orderNo', 'icon', 'component_path as component', 'route_path as routePath',
            'is_hidden as isHidden', 'status', 'created_at as createTime',
        ], [], [
            'orderBy' => '`order` asc'
        ]);

        //组装出次层级关系
        //如果通过名字搜索出来，菜单没有层级关系
        /**
         * 加入一个children
         */

        $tree = Services\Util::cook2Tree($collection, [
            'parent' => 'parentId',
            'child' => 'id',
            'root' => 0
        ]);

        return ApiResponse::success($tree);
    }

    public function getSelectTree(Request $request)
    {
        //返回一个tree，用于select框

        //返回当前用户可以看到的权限
        $user = User::getInstance();
        $collection = $user->getPermissionsInfo([
            'id', 'id as key', 'parent_id as parentId', 'name', 'icon'
        ]);

        $tree = Services\Util::cook2Tree($collection, [
            'parent' => 'parentId',
            'child' => 'id',
            'root' => 0
        ]);

        return ApiResponse::success($tree);
    }

    public function create(Request $request)
    {
        $req = self::paramsFilter($request->post(), [
            'id', 'title', ['menuName', 'name'], 'type', ['parentId', 'parent_id'], ['orderNo', 'order'], 'icon',
            ['isHidden', 'is_hidden'], ['isExt', 'is_iframe'], ['frameSrc', 'frame_src'], ['noCache', 'no_cache'],
            ['routePath', 'route_path'], ['component', 'component_path'], 'api', 'status', 'permission'
        ]);

        $req['parent_id'] = $req['parent_id']??0;

        //检查参数
        $validator = new \App\Admin\Validator\Permission();

        $result = $validator->musts([
            'name', 'title', 'type', 'parent_id', 'order'
        ])->check($req);

        // //更新
        // $result = Models\Permissions::updateOrCreate(['id'=>$req['id']??0],$req);

        // $model = Models\Permissions::create($req);
        // $model->save();

        // $permissionId = $model->id;

        return ApiResponse::success(Models\Permissions::updateOrCreate(['id'=>$req['id']??0],$req));
    }

    public function detail(Request $request)
    {
        $req = [
            'id' => $request->get('id'),
        ];

        $validator = new \App\Admin\Validator\Permission();
        $result = $validator->check($req);

        $info = Models\Permissions::find($req['id'], [
            'id', 'title', 'name as menuName', 'type', 'parent_id as parentId', 'order as orderNo', 'icon',
            'is_hidden as isHidden', 'is_iframe as isExt', 'frame_src as frameSrc', 'no_cache as noCache',
            'route_path as routePath', 'component_path as component', 'api', 'status'
        ]);

        if (empty($info)) {
            return ApiResponse::error(100000, '找不到对应的对象');
        }

        return ApiResponse::success($info);
    }

    public function update(Request $request)
    {
        $req = self::paramsFilter($request->post(), [
            'id', 'title', ['menuName', 'name'], 'type', ['parentId', 'parent_id'], ['orderNo', 'order'], 'icon',
            ['isHidden', 'is_hidden'], ['isExt', 'is_iframe'], ['frameSrc', 'frame_src'], ['noCache', 'no_cache'],
            ['routePath', 'route_path'], ['component', 'component_path'], 'api', 'status'
        ]);

        //检查参数
        $validator = new \App\Admin\Validator\Permission();
        $result = $validator->musts([
            'id'
        ])->check($req);

        //更新
        $result = Models\Permissions::where([
            ['id', '=', $req['id']]
        ])->update($req);

        //如果涉及重要信息,需要立马更新权限

        if ($result == 0) {
            return ApiResponse::error(100000, '找不到对应的对象');
        }

        return ApiResponse::success(null);
    }

    public function delete(Request $request)
    {
        $req = [
            'id' => $request->get('id')
        ];

        $validator = new \App\Admin\Validator\Permission();
        $result = $validator->check($req);

        $result = Models\Permissions::where([
            ['id', '=', $req['id']]
        ])->delete();

        if ($result == 0) {
            return ApiResponse::error(100000, '找不到对应的对象');
        }

        return ApiResponse::success($req['id']);
    }
}
