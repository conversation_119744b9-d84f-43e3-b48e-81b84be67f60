<?php
namespace App\Api\Services;

use Cache;
use App\Management\Models;

class BaseService
{
    const DEFAULT_LIMIT = 20;
    const DEFAULT_PAGE  = 1;
    private $personInfo = null;
    private $deptInfo = null;

    public function paramsFilter($data, $fields, $flag = false)
    {
        $result = [];
        foreach ($fields as $item) {

            if (is_array($item)) {
                $old = $item[0];
                $new = $item[1];
            } else {
                $old = $new = $item;
            }

            if ($flag)
                !empty($data[$old]) && $result[$new] = $data[$old];
            else
                isset($data[$old]) && $result[$new] = $data[$old];
        }

        return $result;
    }

    public static function getCollection($model, $query, $fields, $optionFields = [], $option = []): \Illuminate\Database\Eloquent\Collection
    {
        if (is_array($query)) {
            $query = $model::useDiyWheres($query);
        } else {
            //避免受到下面的查询干扰
            $query = clone $query;
        }

        $query->setFields($fields);

        $query->queryOption($option);

        return $query->get();
    }

    protected function fetchRemoteData()
    {
        $personKey = '_personKey';
        $deptKey = '_deptKey';

        if (Cache::has($personKey) && Cache::has($deptKey)) {
            $personInfo = Cache::get($personKey);
            $deptInfo = Cache::get($deptKey);
        } else {
            //从AUTH获得人员信息
            $personInfo = Models\Account::where('status',1)->get()->toArray();
            dd($personInfo);
            //$personInfo = array_column($personInfo['items'], null, 'id');
            $deptInfo = Models\Department::get();;
            //$deptInfo = array_column($deptInfo['items'], 'name', 'id');

            //有效时间10分钟
            Cache::put($personKey, $personInfo, 60 * 10);
            Cache::put($deptKey, $deptInfo, 60 * 10);
        }

        $this->personInfo = $personInfo;
        $this->deptInfo = $deptInfo;
    }
}
