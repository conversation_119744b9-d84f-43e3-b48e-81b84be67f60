<?php

namespace App\Admin\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\AsArrayObject;

class Roles extends Model
{
    use \App\Api\Util\Query\ModelHelper;

    const TABLE_NAME = 'roles';

    const STATUS_ENABLE = 1;
    const STATUS_DISABLE = 0;

    protected $table = self::TABLE_NAME;

    protected $casts = [
        'permissions' => 'array',
    ];

    protected $fillable = [
        'idname', 'parent_id', 'name', 'note', 'status', 'permissions'
    ];

    protected $whereHandler = [self::class, 'cookWheres'];

    public static function cookWheres($filterValue)
    {
        if (isset($filterValue['name'])) {
            $filterValue[] = ['name', 'like', '%' . $filterValue['name'] . '%'];
            unset($filterValue['name']);
        }

        return $filterValue;
    }

    public function department(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(RolesDepartment::class, 'role_id');
    }
}
