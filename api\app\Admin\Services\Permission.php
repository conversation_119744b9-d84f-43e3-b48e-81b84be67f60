<?php

namespace App\Admin\Services;

use App\Admin\Models;
use App\Exceptions\ServiceException;
use \Illuminate\Database\Eloquent\Collection as EloCollection;
use Illuminate\Support\Arr;
use PhpParser\Node\Arg;
use App\Api\Util\QueryHelper;

class Permission
{
    /**
     * 带有一点逻辑
     * 
     * @param int $roleId 角色id
     * @param array $fields
     * @param array $blackList
     */
    public static function getCollectionByRoleId($roleId, $fields, $blackList = []): EloCollection
    {
        !in_array('id', $fields) && array_unshift($fields, 'id');

        if ($roleId == Role::ROLE_ID_DEVELOPER) {
            //开发人员，通过permission直接全表获取所有权限
            $collection = self::getCollection([], $fields);
        } else {
            //去读取role.permissions
            //超管，普通人，根据role_id通过『role.permission』获取权限
            $permissionIds = Models\Roles::useDiyWheres([
                ['id', '=', $roleId],
                ['status', '=', Models\Roles::STATUS_ENABLE]
            ])->value('permissions');

            $collection = Permission::getCollection([
                'id' => $permissionIds
            ], $fields);
        }

        if (!empty($blackList)) {
            $collection = $collection->filter(function ($value, $key) use ($blackList) {
                return !in_array($value->id, $blackList);
            });
        }

        return $collection;
    }

    public static function getCollection($query, $fields, $optionFields = [], $option = []): \Illuminate\Database\Eloquent\Collection
    {
        if (is_array($query)) {
            $query = Models\Permissions::useDiyWheres($query);
        } else {
            //避免受到下面的查询干扰
            $query = clone $query;
        }

        $query->setFields($fields);

        $query->queryOption($option);

        return $query->get();
    }

    /**
     * 将permission整合成一个给vben前端用的结构
     * collection中的字段请参考 \App\Admin\Service\User::getMenuForPage()
     * 
     * @param \Illuminate\Database\Eloquent\Collection $permission
     */
    public static function cook2Menu($permission): array
    {
        //跳过type=3(取出type=1、2的
        $pList = $permission->filter(function ($pValue, $key) {
            return $pValue->type != Models\Permissions::TYPE_ITEM;
        });

        $rawMenu = Util::cook2Tree($pList, [
            'parent' => 'parent_id',
            'child' => 'id',
            'root' => 0
        ]);

        //拼装menu
        $menu = [];
        foreach ($rawMenu as $menuP) {
            $menu[] = self::cookPModle2MenuItem($menuP);
        }

        return $menu;
    }

    protected static function cookPModle2MenuItem($p, $parentP = null)
    {
        $item = [
            'path' => $p->route_path,
            'meta' => [
                'title' => $p->title,
            ]
        ];

        isset($p->icon) && $item['meta']['icon'] = $p->icon;

        if ($p->type == Models\Permissions::TYPE_MENU) {
            $parentRoute = ($parentP->route_path) ?? '';
            $item['name'] = $parentRoute . '/' . $p->route_path;
            $item['parentId'] = ($parentP->id) ?? 0;
            if (isset($p->children)) {
                $children = [];
                foreach ($p->children as $cp) {
                    $children[] = self::cookPModle2MenuItem($cp, $p);
                }
                $item['children'] = $children;
            }

            if (isset($item['children'][0])) {
                $item['redirect'] = $item['path'] . '/' . $item['children'][0]['path'];
                $item['component'] = 'LAYOUT';
            } else {
                $item['component'] = $p->component_path;
            }
        } elseif ($p->type == Models\Permissions::TYPE_PAGE) {
            $parentRoute = ($parentP->route_path) ?? '';
            $item['name'] = $parentRoute . '/' . $p->route_path;
            $item['component'] = $p->component_path;
            if ($p->no_cache) {
                $item['meta']['ignoreKeepAlive'] = true;
            }
        }

        if ($p->is_hidden) {
            $item['meta']['hideMenu'] = true;
            $item['meta']['hideBreadcrumb'] = true;
            isset($parentP) && $item['meta']['currentActiveMenu'] = $parentP->route_path;
        }

        if ($p->is_iframe) {
            $item['meta']['frameSrc'] = $p->frame_src;
            $item['component'] = 'IFrame';
        }

        return $item;
    }

    public static function getPermissionId($route)
    {
        //查询的是没有索引的列，并且么有
        $value = Models\Permissions::where([
            ['api', '=', $route],
        ])->value('id');

        return $value;
    }
}
