<?php

namespace App\Admin\Models;

use Illuminate\Database\Eloquent\Model;

class AccountLoginInfo extends Model
{
    use \App\Api\Util\Query\ModelHelper;

    const TABLE_NAME = 'account_login_info';

    const P_STATUS_DISABLE = 0;
    const P_STATUS_ENABLE = 1;
    const P_STATUS_EXPIRED = 2;

    protected $table = self::TABLE_NAME;

    protected $casts = [
        'perms' => 'json',
    ];

    protected $whereHandler = [self::class, 'cookWheres'];

    public static function cookWheres($filterValue)
    {
        if (isset($filterValue['role_id'])) {
            $filterValue[] = ['account_id', 'in', function ($query) use ($filterValue) {
                $query->select('id')->from(Account::TABLE_NAME)->where([
                    ['role_id', '=', $filterValue['role_id']]
                ]);
            }];
            unset($filterValue['role_id']);
        }

        return $filterValue;
    }
}
