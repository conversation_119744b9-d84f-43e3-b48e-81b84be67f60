<?php
namespace App\ERP\Controllers\sale;

use App\ERP\Services;
use App\Api\Util\User;
use Illuminate\Http\Request;
use App\Api\Util\ApiResponse;
use Illuminate\Support\Facades\DB;
use App\Api\Util\Enum\ValidationRule;

//包裹管理
class PackageController extends \App\Api\Controllers\ApiController
{
    public function getPageList(Request $request)
    {
        //$req = $params = $request->all();//erp/package/getList
        $req = self::paramsFilter($request->all(), [
            ['title','strid'], 'page', 'pageSize'
        ]);

        $rep = Services\AdminSDK\SalePackage::getPageList(env('ERPFLAG'), $req, $request->header('Authorization'));
        $rep['nav'] = [];
        $rep['nav']['columns'] = [];
        $rep['nav']['columns'][] = ['field'=>'quantity', 'type'=>'text','title'=>'打包件数'];
        $rep['nav']['columns'][] = ['field'=>'method', 'type'=>'text','title'=>'打包方式'];
        $rep['nav']['columns'][] = ['field'=>'length', 'type'=>'text','title'=>'包裹长度'];
        $rep['nav']['columns'][] = ['field'=>'width', 'type'=>'text','title'=>'包裹宽度'];
        $rep['nav']['columns'][] = ['field'=>'height', 'type'=>'text','title'=>'包裹高度'];
        $rep['nav']['columns'][] = ['field'=>'weight', 'type'=>'text','title'=>'包裹重量'];
        $rep['nav']['columns'][] = ['field'=>'inCharge_name', 'type'=>'text','title'=>'负责人'];
        $rep['nav']['columns'][] = ['field'=>'warehouse_name', 'type'=>'text','title'=>'仓库名称'];
        $rep['nav']['columns'][] = ['field'=>'warehouse_item_name', 'type'=>'text','title'=>'仓位名称'];
        $rep['nav']['columns'][] = ['field'=>'is_old', 'type'=>'select','title'=>'是否旧包裹', 'options'=>[0=>'否',1=>'是']];
        $rep['nav']['columns'][] = ['field'=>'is_in', 'type'=>'select','title'=>'是否已入库', 'options'=>[0=>'否',1=>'是']];
        $rep['nav']['columns'][] = ['field'=>'is_out', 'type'=>'select','title'=>'是否已出库', 'options'=>[0=>'否',1=>'是']];
        $rep['nav']['columns'][] = ['field'=>'creator_name', 'type'=>'text','title'=>'创建人'];
        $rep['nav']['columns'][] = ['field'=>'created_at', 'type'=>'text','title'=>'创建时间'];
        //$rep['nav']['columns'][] = ['field'=>'status', 'type'=>'text','title'=>'状态'];  //0待审核 1已审核 15结束


        return ApiResponse::success($rep);
    }

    public function create(Request $request)
    {
        $req = self::paramsFilter($request->post(), [
            'id', 'height', 'inCharge', 'length', 'method' ,'volume', 'weight',
            'width', 'supplier_strid'
        ]);

        $model = Services\AdminSDK\SalePackage::create(env('ERPFLAG'), $req, $request->header('Authorization'));

        if (!$model)
            return ApiResponse::error(100000, '创建失败');

        return ApiResponse::success($model);
    }

    public function detail(Request $request)
    {
        $rep = [];

        $req = self::paramsFilter($request->all(), [
            ['id', 'ids']
        ]);

        if(!empty($req['ids']))
        {
            $req['ids'] = is_array($req['ids'])?$req['ids']:explode(',', $req['ids']);
            $rep = Services\AdminSDK\SalePackage::detail(env('ERPFLAG'), $req, $request->header('Authorization'));
        }
        $rep['items'] = !empty($rep['items'])&&is_array($rep['items'])?end($rep['items']):[];

        $dataItem = [];
        $dataItem[0]['field'] = 'method';
        $dataItem[0]['title'] = '打包方式';
        $dataItem[0]['type'] = 'select';
        $dataItem[0]['options'] = ['木箱打包'=>'木箱打包','纸箱打包'=>'纸箱打包'];

        $dataItem[1]['field'] = 'length';
        $dataItem[1]['title'] = '长（cm）';
        $dataItem[1]['type'] = 'number';

        $dataItem[2]['field'] = 'width';
        $dataItem[2]['title'] = '宽（cm）';
        $dataItem[2]['type'] = 'number';

        $dataItem[3]['field'] = 'height';
        $dataItem[3]['title'] = '高（cm）';
        $dataItem[3]['type'] = 'number';

        $dataItem[4]['field'] = 'weight';
        $dataItem[4]['title'] = '重量(KG)';
        $dataItem[4]['type'] = 'number';

        $dataItem[5]['field'] = 'volume';
        $dataItem[5]['title'] = '体积(CBM)立方米';
        $dataItem[5]['type'] = 'number';

        $dataItem[6]['field'] = 'supplier_strid';
        $dataItem[6]['title'] = '供应商箱号';
        $dataItem[6]['type'] = 'text';

        $dataItem[7]['field'] = 'inCharge';
        $dataItem[7]['title'] = '负责人';
        $dataItem[7]['type'] = 'person';

        $rep['formData'] = [];
        $rep['formData'][0] = ['label'=>'基本信息', 'dataItem'=>$dataItem];
        return ApiResponse::success($rep);
    }

    public function info(Request $request)
    {
        $req = self::paramsFilter($request->input(), [
            'system','entity'
        ]);

        $validator = new \App\Management\Validator\ProcessSet();
        $validator->musts(['system', 'entity'])->check($req);

        $info = Models\ProcessSet::where($req)->first();

        if (empty($info))
            return ApiResponse::error(100000, '数据不存在或已被删除');

        return ApiResponse::success($info);
    }

    public function delete(Request $request)
    {
        $req = self::paramsFilter($request->post(), [
            'id'
        ]);

        $validator = new \App\Management\Validator\ProcessSet();

        $validator->musts(['id'])->check($req);

        $result = Models\SalePackage::withCount('processMain')->find($req['id']);

        if(empty($result))
            return ApiResponse::success($req['id']);

        if($result->process_main_count>0)
            return ApiResponse::error(100000, '不能删除进行中的流程');

        $result->delete();

        return ApiResponse::success($req['id']);
    }
}
