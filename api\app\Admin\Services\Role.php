<?php

namespace App\Admin\Services;

use App\Admin\Models;
use App\Exceptions\ServiceException;
use \Illuminate\Database\Eloquent\Collection as EloCollection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Arr;
use App\Api\Util\User;
use App\Api\Util\QueryHelper;

class Role
{
    const ROLE_ID_ROOT = 0; //并没有实际的用途
    const ROLE_ID_DEVELOPER = 1;
    const ROLE_ID_SUPER_ADMIN = 2;
    const ROLE_ID_NO_PERMISSION = 3; //没有任何权限

    public static function getCollection($query, $fields, $optionFields = [], $option = []): \Illuminate\Database\Eloquent\Collection
    {
        if (is_array($query)) {
            $query = Models\Roles::useDiyWheres($query);
        } else {
            //避免受到下面的查询干扰
            $query = clone $query;
        }

        $query->setFields($fields);

        $query->queryOption($option);

        return $query->get();
    }

    public static function getPermissionsByRoleId($roleId): array
    {
        if ($roleId != Role::ROLE_ID_DEVELOPER) {
            $permissionIds = Models\Roles::newDiyQuery()->where([
                ['id', '=', $roleId],
                ['status', '=', Models\Roles::STATUS_ENABLE]
            ])->value('permissions');

            return $permissionIds;
        }

        $pCollection = Permission::getCollectionByRoleId($roleId, ['id']);
        return Arr::pluck($pCollection->all(), 'id');;
    }

    /**
     * @param array $where 用于查询的参数数组
     * @param string $roleIdField 筛选的字段名
     */
    public static function withoutHiddenRole(&$where, $roleIdField = 'role_id')
    {
        $user = User::getInstance();

        if ($user->roleId != self::ROLE_ID_DEVELOPER) {
            if ($user->roleId == self::ROLE_ID_SUPER_ADMIN) {
                $where[] = [$roleIdField, '>', self::ROLE_ID_DEVELOPER];
            } else {
                $where[] = [$roleIdField, '>', self::ROLE_ID_SUPER_ADMIN];
            }
        }
    }
}
