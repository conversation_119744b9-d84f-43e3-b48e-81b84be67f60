<?php
namespace App\Api\Controllers;

use App\Api\Util;
use App\Admin\Models;
use App\Admin\Services;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Exceptions\ServiceException;
use Illuminate\Support\Facades\Route;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class ApiController extends BaseController
{
    // use AuthorizesRequests, DispatchesJobs, ValidatesRequests;

    // public function __construct()
    // {
    //     //反正就在这里直接注册一个中间件，懒得在外面写一个还要注册了
    //     $this->middleware(function ($request, $next) {

    //     })->except('login');
    // }

    public static function paramsFilter($data, $fields)
    {
        $result = [];
        foreach ($fields as $item) {
            if (is_array($item)) {
                $old = $item[0];
                $new = $item[1];
            } else {
                $old = $new = $item;
            }

            isset($data[$old]) && $result[$new] = $data[$old];
        }

        return $result;
    }

    public function getParams(Request $request)
    {
        $params = $request->all();
        $user = Util\User::getInstance();
        $params['person_id'] = $user->userId??0;
        $params['dept_id'] = $user->deptId??0;
        unset($params['_t']);

        return $params;
    }
}
