<?php
namespace App\Api\Models;

use DateTimeInterface;
use App\Api\Util\Query\ModelHelper;
use Illuminate\Database\Eloquent\Model;

class BaseModel extends Model
{
    use ModelHelper;

    protected $whereHandler = [self::class, 'cookWheres'];

    //状态，0待确认，1启用，2禁用, 4已归档 8草稿，9作废
    const STATUS_WAIT_CONFIRM = 0;
    const STATUS_SUBMITED = 1;
    const STATUS_CANCEL = 2;
    const STATUS_FINISH = 4;
    const STATUS_DRAFT = 8;
    const STATUS_DELETED = 9;


    // 类型
    const TYPE_ALL    = 0;
    const TYPE_ONE    = 1;
    const TYPE_TWO    = 2;
    const TYPE_THREE  = 3;
    const TYPE_FOUR   = 4;
    const TYPE_FIVE   = 5;
    const TYPE_SIX    = 6;
    const TYPE_SEVEN  = 7;
    const TYPE_EIGHT  = 8;
    const TYPE_NINE   = 9;
    const TYPE_TEN    = 10;

    //搜索表单
    public function getSearchForm(): array
    {
        //搜索
        return [
            0 => [
                'label' => '基本信息',
                'dataItem' => [
                    ['field' => 'code', 'type' => 'text', 'title' => '编号'],
                    ['field' => 'title', 'type' => 'text', 'title' => '名称']
                ]
            ],
        ];
    }

    public static array $formNav = [
    	0 => ['name' => '待跟进', 'value' => 0, 'key' => '', 'num' => 0],
    	1 => ['name' => '已跟进', 'value' => 4, 'key' => '', 'num' => 0],
    	2 => ['name' => '已完成', 'value' => 5, 'key' => '', 'num' => 0],
    ];

    public static array $detailNav = [
    	0 => ['name'=>'操作记录','value'=>0,'type'=>1],
    	1 => ['name'=>'详情信息','value'=>1,'type'=>2],
    ];

    /**
     * 表单配置
     */
    public function getFormData(): array
    {
        return [];
    }

    public function detailColumns(): array
    {
        return [];
    }

    public static function cookWheres($filterValue)
    {
        if (isset($filterValue['code'])) {
            $filterValue[] = ['code', 'like', '%' . $filterValue['code'] . '%'];
            unset($filterValue['code']);
        }

        if (isset($filterValue['title'])) {
            $filterValue[] = ['title', 'like', '%' . $filterValue['title'] . '%'];
            unset($filterValue['title']);
        }

        if (isset($filterValue['dept_ids'])) {
            $filterValue[] = ['dept_id', 'in', !is_array($filterValue['dept_ids'])?explode(',', $filterValue['dept_ids']):$filterValue['dept_ids']];
            unset($filterValue['dept_ids']);
        }

        if (isset($filterValue['ids'])) {
            $filterValue[] = ['id', 'in', !is_array($filterValue['ids'])?explode(',', $filterValue['ids']):$filterValue['ids']];
            unset($filterValue['ids']);
        }

        if (isset($filterValue['tabIndex'])) {
            unset($filterValue['tabIndex']);
        }

        return $filterValue;
    }

    //前置回调
    public function callProcessBefore($processRun)
    {

    }

    //后置回调
    public function callProcess($processRun)
    {

    }
}
