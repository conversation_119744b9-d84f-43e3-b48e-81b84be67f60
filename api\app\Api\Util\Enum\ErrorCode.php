<?php

namespace App\Api\Util\Enum;

class ErrorCode
{
    //用于拦截其它Throwable错误
    const ERROR = 5500;

    //出现这里的错误会被直接弹回登陆框
    const TOKEN_NOF_FOUND = 1000;
    const TOKEN_INVALID = 1001;
    const TOKEN_EXPIRE = 1002;
    const TOKEN_BEFORE_VALID = 1003;
    const TOKEN_VERIFY_FAIL = 1004;
    const TOKEN_PAYLOAD_INVALID = 1005;
    const LOGIN_INFO_CREATE_FAIL = 1101;
    const LOGIN_INFO_NOT_FOUND = 1102;
    const LOGIN_INFO_TOKEN_EXPIRE = 1103;

    //以下的不会
    const PERMISSION_DENIED = 2000;
    const PERMISSION_RESTICTED = 2001;
}
