<?php
namespace App\CRM\Controllers\dealt;

use App\CRM\Services;
use App\Api\Util\User;
use Illuminate\Http\Request;
use App\Api\Util\ApiResponse;
use Illuminate\Support\Facades\DB;
use App\Api\Util\Enum\ValidationRule;

//待跟回款单
class PayreceiptController extends \App\Api\Controllers\ApiController
{
    public function getPageList(Request $request)
    {
        $req = $params = $request->all();

        $rep = Services\AdminSDK\DealtPayreceipt::getPageList('CRM', $req, $request->header('Authorization'));

        return ApiResponse::success($rep);
    }

    public function create(Request $request)
    {
        $req = self::paramsFilter($request->post(), [
            'name', 'icon', 'color', 'process_type_id' ,'type', 'system',
            'department_id', 'sort', 'entity', 'entity_field', 'note', 'status'
        ]);
        $req['status'] = Models\ProcessSet::STATUS_ENABLE;
        $req['entity'] = $req['entity']??'';
        $req['entity_field'] = $req['entity_field']??'';
        $req['note'] = $req['note']??'';
        $req['department_id'] = $req['department_id']??0;
        $req['sort'] = $req['sort']??0;
        //检查参数
        $validator = new \App\Management\Validator\ProcessSet();
        $validator->musts([
            'name', 'icon', 'color' ,'type', 'system',
            'note', 'status'
        ])->check($req);

        $model = Models\ProcessSet::create($req);

        if (!$model)
            return ApiResponse::error(100000, '创建失败');

        return ApiResponse::success($model);
    }

    public function update(Request $request)
    {
        $req = self::paramsFilter($request->post(), [
            'id', 'name', 'icon', 'color', 'process_type_id' ,'type', 'system',
            'department_id', 'sort', 'entity', 'entity_field', 'note', 'status', 'form_data'
        ]);

        //检查参数
        $validator = new \App\Management\Validator\ProcessSet();
        $validator->musts([
            'id'
        ])->check($req);

        $result = Models\ProcessSet::find($req['id']);;

        if(empty($result))
            return ApiResponse::error(100000, '数据不存在或已被删除');

        return ApiResponse::success($result->update($req));
    }

    public function detail(Request $request)
    {
        $req = self::paramsFilter($request->input(), [
            'id'
        ]);

        $validator = new \App\Management\Validator\ProcessSet();
        $validator->musts(['id'])->check($req);

        $info = Models\ProcessSet::find($req['id']);

        if (empty($info))
            return ApiResponse::error(100000, '数据不存在或已被删除');

        return ApiResponse::success($info);
    }

    public function info(Request $request)
    {
        $req = self::paramsFilter($request->input(), [
            'system','entity'
        ]);

        $validator = new \App\Management\Validator\ProcessSet();
        $validator->musts(['system', 'entity'])->check($req);

        $info = Models\ProcessSet::where($req)->first();

        if (empty($info))
            return ApiResponse::error(100000, '数据不存在或已被删除');

        return ApiResponse::success($info);
    }

    public function delete(Request $request)
    {
        $req = self::paramsFilter($request->post(), [
            'id'
        ]);

        $validator = new \App\Management\Validator\ProcessSet();

        $validator->musts(['id'])->check($req);

        $result = Models\ProcessSet::withCount('processMain')->find($req['id']);

        if(empty($result))
            return ApiResponse::success($req['id']);

        if($result->process_main_count>0)
            return ApiResponse::error(100000, '不能删除进行中的流程');

        $result->delete();

        return ApiResponse::success($req['id']);
    }

    public function getClientList(Request $request)
    {
        $sysList = array_keys(\App\Management\Services\AdminSDK\Util::getSystem());

        return ApiResponse::success($sysList);
    }
}
