<?php

namespace App\Admin\Controllers;

use App\Api\Util\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Admin\Models;
use App\Admin\Services;
use App\Api\Util\Enum\ValidationRule;
use App\Api\Util\User;
use Illuminate\Support\Arr;

class AccountController extends \App\Api\Controllers\ApiController
{
    public function getPageList(Request $request)
    {
        $req = self::paramsFilter($request->input(), [
            ['deptId', 'dept_id'], 'name', 'status'
        ]);

        $validator = new \App\Admin\Validator\Account();
        $result = $validator->otherRules(ValidationRule::PAGINATION)->check($req);

        //准备用户信息
        $user = User::getInstance();

        $page = $request->input('page', 1);
        $pageSize = $request->input('pageSize', 10);

        $where = $req;
        //每个角色只能看到自己当前所处的部门下的用户（数据权限划分）
        //某个超管角色只隶属于某个部门也只能看某个部门内的消息
        if ($user->basicDeptId != Services\Department::ID_COMPANY) {
            $where['basic_dept_id'] = $user->basicDeptId;
        }

        //开发人员和超管的账号貌似不应该显示出来
        Services\Role::withoutHiddenRole($where, 'role_id');

        $query = Models\Account::useDiyWheres($where);

        $collection = Services\Account::getCollection($query, [
            //应该要将部门信息翻译成部门名字
            'id', 'username', 'name', 'role_id as roleId',
            'status', 'created_at as createTime', 'description as remark'
        ], ['roleName'], [
            'offset' => ($page - 1) * $pageSize,
            'limit' => $pageSize
        ]);

        $list = $collection->all();

        $total = $query->count('id');

        return ApiResponse::pagination($list, $total);
    }

    public function getSelectList(Request $request)
    {
        //所以这样还需要先由上层查找到一个部门id

        $req = self::paramsFilter($request->input(), [
            ['deptId', 'basic_dept_id'],
        ]);

        $validator = new \App\Admin\Validator\Account();
        $result = $validator->musts(['basic_dept_id'])->check($req);

        $where = $req;
        $query = Models\Account::useDiyWheres($where);

        $collection = Services\Account::getCollection($query, [
            //应该要将部门信息翻译成部门名字
            'id', 'id as key', 'name',
        ]);

        return ApiResponse::success($collection);
    }

    public function checkExist(Request $request)
    {
        $req = [
            'username' => $request->get('username'),
        ];

        $validator = new \App\Admin\Validator\Account();
        $result = $validator->check($req);

        $flag = Models\Account::where($req)->exists();

        if ($flag) return ApiResponse::error(100000, '当前账号名已经存在');

        return ApiResponse::success('');
    }

    public function create(Request $request)
    {
        $req = self::paramsFilter($request->post(), [
            'unionid', 'name', 'username', ['roleId', 'role_id'], ['deptId', 'dept_id'],
            'status', ['remark', 'description'], ['pwd', 'password'], 'permissions'
        ]);

        $validator = new \App\Admin\Validator\Account();
        $result = $validator->musts([
            'name', 'username', 'role_id', 'dept_id', 'status', 'password', 'permissions'
        ])->otherRules([
            'permissions' => ['array']
        ])->check($req);

        //取出当前角色的对应的权限，如果没有新传入的需要找一次数据库
        $pids = Services\Role::getPermissionsByRoleId($req['role_id']);

        //然后比对传入的权限
        $blackListIds = array_diff($pids, $req['permissions']);
        unset($req['permissions']);
        $req['pids_blacklist'] = array_values($blackListIds);

        //取出当前部门id对应的basic_dept_id
        $req['basic_dept_id'] = Services\Department::getBasicDeptId($req['dept_id']);

        $model = Models\Account::create($req);
        if (!$model->save()) {
            return ApiResponse::error(100000, '创建失败');
        }

        $id = $model->id;
        return ApiResponse::success([
            'id' => $id
        ]);
    }

    public function update(Request $request)
    {
        $req = self::paramsFilter($request->post(), [
            'id', 'name', ['roleId', 'role_id'], ['deptId', 'dept_id'],
            'status', ['remark', 'description'], ['pwd', 'password'],  'permissions'
        ]);

        $validator = new \App\Admin\Validator\Account();
        $result = $validator->musts([
            'id', 'role_id'
        ])->otherRules([
            'permissions' => ['array']
        ])->check($req);

        //这里修改是不能直接修改它的权限可见范围

        if (array_key_exists('permissions', $req)) {
            //取出当前角色的对应的权限，如果没有新传入的需要找一次数据库
            $pids = Services\Role::getPermissionsByRoleId($req['role_id']);

            //然后比对传入的
            $blackListIds = array_diff($pids, $req['permissions']);
            unset($req['permissions']);
            $req['pids_blacklist'] = array_values($blackListIds);
        }

        if (array_key_exists('dept_id', $req)) {
            //取出当前部门id对应的basic_dept_id
            $req['basic_dept_id'] = Services\Department::getBasicDeptId($req['dept_id']);
        }

        $result = Models\Account::where([
            ['id', '=', $req['id']]
        ])->update($req);

        if ($result == 0) {
            return ApiResponse::error(100000, '更新失败');
        }

        return ApiResponse::success(null);
    }

    public function detail(Request $request)
    {
        $req = [
            'id' => $request->get('id'),
        ];

        $validator = new \App\Admin\Validator\Account();
        $result = $validator->check($req);

        $info = Models\Account::find($req['id'], [
            'id', 'name', 'username',
            'role_id as roleId', 'dept_id as deptId', 'pids_blacklist',
            'status', 'description as remark'
        ]);

        if (empty($info)) {
            return ApiResponse::error(100000, '找不到对应的对象');
        }

        $pcollection = Services\Permission::getCollectionByRoleId($info->roleId, ['id'], $info->pids_blacklist);

        $info->permissions = Arr::pluck($pcollection->all(), 'id');

        unset($info->pids_blacklist);

        return ApiResponse::success($info);
    }

    public function delete(Request $request)
    {
        //人员不能真正地删除，如果涉及到过往的操作信息


    }
}
