<?php

namespace App\Admin\Validator;

use Illuminate\Validation\Rule;
use App\Admin\Models;

class Department extends \App\Api\Util\CustomerValidator
{
    public function __construct()
    {
        $this->rules = [
            'id' => ['integer'],
            'pidStr' => ['string', 'max:255', 'regex:/^_(?:[0-9]_)+$/'],
            'parent_id' => ['integer'],
            'note' => ['string', 'max:255'],
            'status' => [
                Rule::in([
                    Models\Department::STATUS_DISABLE,
                    Models\Department::STATUS_ENABLE,
                ]),
            ]
        ];
    }
}
