<?php
namespace App\ERP\Controllers\sale;

use App\ERP\Services;
use App\Api\Util\User;
use Illuminate\Http\Request;
use App\Api\Util\ApiResponse;
use Illuminate\Support\Facades\DB;
use App\Api\Util\Enum\ValidationRule;

//备货管理
class StorageupController extends \App\Api\Controllers\ApiController
{
    public function getPageList(Request $request)
    {
        $req = self::paramsFilter($request->all(), [
            ['title','strid'], 'page', 'pageSize'
        ]);

        $rep = Services\AdminSDK\SaleEncasement::getPageList(env('ERPFLAG'), $req, $request->header('Authorization'));
        $rep['nav'] = [];
        $rep['nav']['columns'] = [];
        $rep['nav']['columns'][] = ['field'=>'buyer', 'type'=>'text','title'=>'购买方'];
        $rep['nav']['columns'][] = ['field'=>'country', 'type'=>'text','title'=>'国家'];
        $rep['nav']['columns'][] = ['field'=>'supplier', 'type'=>'text','title'=>'供应商'];
        $rep['nav']['columns'][] = ['field'=>'shipment_addr', 'type'=>'text','title'=>'出货地址'];
        $rep['nav']['columns'][] = ['field'=>'shipment_at', 'type'=>'date','title'=>'出货日期'];
        $rep['nav']['columns'][] = ['field'=>'created_at', 'type'=>'text','title'=>'创建时间'];

        return ApiResponse::success($rep);
    }

    public function create(Request $request)
    {
        $req = self::paramsFilter($request->post(), [
            'strid', 'packing_id'
        ]);

        $rep = Services\AdminSDK\SaleStorageup::create(env('ERPFLAG'), ['stockList'=>[$req]], $request->header('Authorization'));

        return ApiResponse::success($rep);
    }

    public function upimg(Request $request)
    {
        $req = self::paramsFilter($request->post(), [
            'id', ['imgs','stock_imgs']
        ]);

        //$rep = Services\AdminSDK\SaleStorageup::create(env('ERPFLAG'), ['stockList'=>[$req]], $request->header('Authorization'));
        $rep = Services\AdminSDK\SaleStorageup::upimg(env('ERPFLAG'), $req, $request->header('Authorization'));
        return ApiResponse::success($rep);
    }

    public function detail(Request $request)
    {
        $req = self::paramsFilter($request->all(), [
            'id'
        ]);

        $rep = [];
        if(!empty($req['id']))
        {
            $rep = Services\AdminSDK\SaleEncasement::detail(env('ERPFLAG'), $req, $request->header('Authorization'));
        }

        $dataItem = [];
        $dataItem[0]['field'] = 'urgent_level';
        $dataItem[0]['title'] = '紧急程度';
        $dataItem[0]['type'] = 'select';
        $dataItem[0]['required'] = true;
        $dataItem[0]['options'] = [1=>'一般',2=>'紧急',3=>'非常紧急'];

        $dataItem[1]['field'] = 'buyer';
        $dataItem[1]['title'] = '买方';
        $dataItem[1]['type'] = 'text';
        $dataItem[1]['required'] = true;

        $dataItem[2]['field'] = 'country';
        $dataItem[2]['title'] = '国家';
        $dataItem[2]['type'] = 'text';
        $dataItem[2]['required'] = true;

        $dataItem[3]['field'] = 'cabinet_number';
        $dataItem[3]['title'] = '柜号';
        $dataItem[3]['type'] = 'text';

        $dataItem[4]['field'] = 'plate_number';
        $dataItem[4]['title'] = '车牌号';
        $dataItem[4]['type'] = 'text';

        $dataItem[5]['field'] = 'supplier';
        $dataItem[5]['title'] = '供应商';
        $dataItem[5]['type'] = 'text';
        $dataItem[5]['required'] = true;

        $dataItem[6]['field'] = 'shipmentAt';
        $dataItem[6]['title'] = '出货日期';
        $dataItem[6]['type'] = 'date';
        $dataItem[6]['required'] = true;

        $dataItem[7]['field'] = 'shipmentAddr';
        $dataItem[7]['title'] = '出货地址';
        $dataItem[7]['type'] = 'text';
        $dataItem[7]['required'] = true;

        $rep['formData'] = [];
        $rep['formData'][0] = ['label'=>'基本信息', 'dataItem'=>$dataItem];
        return ApiResponse::success($rep);
    }

    public function delete(Request $request)
    {
        $req = self::paramsFilter($request->post(), [
            'id', 'packing_package_id'
        ]);
       // dd($req);
        Services\AdminSDK\SaleEncasement::delete(env('ERPFLAG'), $req, $request->header('Authorization'));

        return ApiResponse::success($req);
    }
}
