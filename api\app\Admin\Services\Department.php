<?php

namespace App\Admin\Services;

use App\Admin\Models;
use App\Exceptions\ServiceException;
use \Illuminate\Database\Eloquent\Collection as EloCollection;
use Illuminate\Support\Facades\DB;
use App\Api\Util\QueryHelper;

class Department
{
    const ID_COMPANY = 1;
    const ID_WAIT_CONFIRM = 3;

    public static function getCollection($query, $fields, $optionFields = [], $option = []): \Illuminate\Database\Eloquent\Collection
    {
        if (is_array($query)) {
            $query = Models\Department::useDiyWheres($query);
        } else {
            //避免受到下面的查询干扰
            $query = clone $query;
        }

        $query->setFields($fields);

        $query->queryOption($option);

        return $query->get();
    }

    public static function getBasicDeptId($deptId)
    {
        //如果是老总、合伙人、管理人员级别的，应该都被规划到某个部门里面
        if ($deptId == self::ID_COMPANY) {
            return $deptId;
        }

        //如果只是普通的部门
        $pidstr = Models\Department::where('id', '=', $deptId)->value('pidstr');
        //移除前后的后缀
        $pidstr = substr($pidstr, 1, strlen($pidstr) - 1);

        $pids = explode('_', $pidstr);

        //返回第二层的大部门id
        return $pids[0];
    }

    public static function getPidStr($parentId)
    {
        $parentDepartmentpidStr = '_';
        if ($parentId > 0) {
            $parentDepartmentpidStr = models\Department::where('id', '=', $parentId)->value('pidStr');
        }

        return $parentDepartmentpidStr . $parentId . '_';
    }
}
