<?php

namespace App\Admin\Services;

class Util
{
    /**
     * 将pcollection整合成一个树状,用于给权限界面使用
     * 
     * @param \Illuminate\Database\Eloquent\Collection $collection
     * @param array
     */
    public static function cook2Tree($collection, $option): array
    {
        //只要有parentId列和id列，由上层自己保证
        $pList = json_decode($collection->toJson());

        $parentIdField = ($option['parent']) ?? 'parent_id';
        $childrenIdField = ($option['child']) ?? 'id';
        $rootId = ($option['root']) ?? 0;
        //还有children可以指定名字，还是算了

        //初始化 nodeMap
        $tempMap = [];
        foreach ($pList as &$p) {
            $tempMap[$p->$childrenIdField] = &$p;
            $p->children = [];
        }

        $tree = [];
        $hasRootNode = false;
        //如果存在父节点信息，则设置该节点信息为root节点
        if (array_key_exists($rootId, $tempMap)) {
            $tree[] = $tempMap[$rootId];
            $hasRootNode = true;
        }

        foreach ($pList as &$p) {
            if (!$hasRootNode && $p->$parentIdField == $rootId) {
                //如果没有rootNode，那就在里面展开
                $tree[] = &$p;
            } else {
                if (!isset($tempMap[$p->$parentIdField])) {
                    //防止数据错误时，取到了一些没有父节点的元素，直接抛弃
                    continue;
                }
                $tempMap[$p->$parentIdField]->children[] = &$p;
            }
        }

        foreach ($tempMap as &$item) {
            if (empty($item->children)) unset($item->children);
        }

        return $tree;
    }
}
