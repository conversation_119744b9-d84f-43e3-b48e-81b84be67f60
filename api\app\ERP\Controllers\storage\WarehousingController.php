<?php
namespace App\ERP\Controllers\storage;

use App\ERP\Services;
use App\Api\Util\User;
use Illuminate\Http\Request;
use App\Api\Util\ApiResponse;
use Illuminate\Support\Facades\DB;
use App\Api\Util\Enum\ValidationRule;

//仓库管理
class WarehousingController extends \App\Api\Controllers\ApiController
{
    public function getPageList(Request $request)
    {
        $req = self::paramsFilter($request->all(), [
            'strid', 'page'
        ]);
        $rep = Services\AdminSDK\Warehousing::getPageList(env('ERPFLAG'), $req, $request->header('Authorization'));
        $rep['items']=array_map(function (&$v){
            $v['packageListNum'] = count($v['packageList']);
            return $v;
        }, $rep['items']);

        $rep['nav'] = [];
        $rep['nav']['columns'] = [];
        $rep['nav']['columns'][] = ['field'=>'remark', 'type'=>'text','title'=>'备注'];
        $rep['nav']['columns'][] = ['field'=>'creator_name', 'type'=>'text','title'=>'创建人'];
        $rep['nav']['columns'][] = ['field'=>'packageListNum', 'type'=>'text','title'=>'包裹数'];
        $rep['nav']['columns'][] = ['field'=>'created_at', 'type'=>'text','title'=>'创建时间'];
        return ApiResponse::success($rep);
    }

    public function create(Request $request)
    {
        return ApiResponse::success([]);
    }

    public function change(Request $request)
    {
        $req = self::paramsFilter($request->input(), [
            '_type', 'id'
        ]);

        return ApiResponse::success([]);
    }

    public function detail(Request $request)
    {
        $req = self::paramsFilter($request->all(), [
            'id','strid'
        ]);
        $rep = [];
        $rep = isset($req['strid'])||isset($req['id'])?Services\AdminSDK\Warehousing::detail(env('ERPFLAG'), $req, $request->header('Authorization')):[];
        if(empty($rep)){
            return ApiResponse::error('10000','没有获取到信息');
        }
        $result=$rep['items'][0]??[];
        if(empty($result)){
            return ApiResponse::error('10000','没有获取到信息');
        }
        $result['packageListNum']=count($result['packageList']);
        //统计result重packageList数组重的warehouse_id数量
        $packageList=$result['packageList'];
        $packageList_count=array_unique(array_column($packageList,'warehouse_id'));
        $result['packageListWNum']=count($packageList_count);

        return ApiResponse::success($result);
    }

    public function delete(Request $request)
    {
        $req = self::paramsFilter($request->post(), [
            'id'
        ]);

        return ApiResponse::success($req['id']);
    }
}
