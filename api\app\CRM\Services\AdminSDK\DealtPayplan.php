<?php
namespace App\CRM\Services\AdminSDK;

use App\Management\Services\AdminSDK\RequestService;

class DealtPayplan
{
    /**
     * @param array $req 用于请求创建用户接口的参数
     */
    public static function getPageList($system, $req, $token)
    {
        $s = new RequestService($system, $token);
        return $s->get('/dealt/payplan/getPageList', $req);
    }

    public static function create($system, $req, $token)
    {
        $s = new RequestService($system, $token);
        return $s->postJson('/dealt/payplan/create', $req);
    }

    public static function detail($system, $req, $token)
    {
        $s = new RequestService($system, $token);
        return $s->get('/dealt/payplan/detail', $req);
    }

    public static function delete($system, $req, $token)
    {
        $s = new RequestService($system, $token);
        return $s->get('/dealt/payplan/delete', $req);
    }
}
