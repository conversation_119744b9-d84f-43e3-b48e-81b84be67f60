<?php

namespace App\Api\Util;

use App\Exceptions\ValidationException;
use Illuminate\Support\Facades\Validator;

/**
 * 一个方便进行校验的验证器
 * php >= 7.3
 */
class CustomerValidator
{
    /**
     * 这里的rule参考laravel的valite规则
     * 每个子项都是array
     * https://laravel.com/docs/7.x/validation#available-validation-rules
     */
    protected array $rules = [];

    /**
     * 这里的message参考laravel的valite的message规则
     * https://laravel.com/docs/7.x/validation#working-with-error-messages
     */
    protected array $messages = [];

    /**
     * 添加必须的字段
     */
    public function musts($fields)
    {
        foreach ($fields as $field) {
            // !isset($this->rules[$field]) && 

            if (!isset($this->rules[$field])) {
                $this->rules[$field] = [];
            }
            array_unshift($this->rules[$field], 'present');
        }

        return $this;
    }

    /**
     * 用于在校验前设定一些操作
     * 譬如实现依赖性的额外检查
     * 
     * @param $data 待检查的数据
     */
    public function cookRulesBeforeCheck($data)
    {
    }

    public function otherRules($rules)
    {
        $this->rules = array_merge($this->rules, $rules);
        return $this;
    }

    public function otherMessages($messages)
    {
        $this->messages = array_merge($this->messages, $messages);
        return $this;
    }

    public function check($data)
    {
        //检查前的回调
        $this->cookRulesBeforeCheck($data);

        $validator = Validator::make($data, $this->rules, $this->messages);

        try {
            $result = $validator->validate();
        } catch (\Illuminate\Validation\ValidationException $e) {
            $newException = new ValidationException();
            $newException->setErrors($validator->errors()->all());

            throw $newException;
        }

        return $result;
    }
}
