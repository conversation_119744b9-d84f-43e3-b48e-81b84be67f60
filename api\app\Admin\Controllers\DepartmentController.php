<?php

namespace App\Admin\Controllers;

use App\Api\Util\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Admin\Models;
use App\Admin\Services;
use App\Api\Util\User;
use App\Api\Util\Enum\ValidationRule;

class DepartmentController extends \App\Api\Controllers\ApiController
{
    public function getTree(Request $request)
    {
        $req = self::paramsFilter($request->input(), [
            'status'
        ]);

        $validator = new \App\Admin\Validator\Department();
        $result = $validator->check($req);

        //准备用户信息
        $user = User::getInstance();

        $where = $req;
        //每个角色只能看到自己当前所处的部门下的部门（数据权限划分）
        if ($user->basicDeptId == Services\Department::ID_COMPANY) {
            $treeRootId = 0;
        } else {
            $treeRootId = $user->basicDeptId;
            // $treeRootId = 0;
            $where['pidstr'] = $user->basicDeptId;
        }

        $query = Models\Department::useDiyWheres($where);

        $collection = Services\Department::getCollection($query, [
            'id', 'name as deptName', 'parent_id as parentId',
            'status', 'created_at as createTime', 'note as remark'
        ]);

        //由于数据不多，所以对pidstr的筛选工作放在php上操作
        // $preg = '/_(?:' . implode('|', $treeRootIds) . ')_/';
        // $collection = $collection->filter(function ($v, $k) use ($preg) {
        //     return preg_match($preg, $v);
        // });

        $tree = Services\Util::cook2Tree($collection, [
            'parent' => 'parentId',
            'child' => 'id',
            'root' => $treeRootId
        ]);

        return ApiResponse::success($tree);
    }

    public function getSelectTree(Request $request)
    {
        $user = User::getInstance();

        //因为ant-design默认的搜索字段是title，为了前端少些代码应该写title
        $collection = $user->getDepartmentInfo([
            'id', 'id as key', 'name', 'parent_id as parentId'
            // 'id', 'name', 'parent_id as parentId'
        ]);

        $tree = Services\Util::cook2Tree($collection, [
            'parent' => 'parentId',
            'child' => 'id',
            'root' => $user->basicDeptId
        ]);

        return ApiResponse::success($tree);
    }

    public function create(Request $request)
    {
        $req = self::paramsFilter($request->post(), [
            ['parentId', 'parent_id'], ['deptName', 'name'], 'status', ['remark', 'note']
        ]);

        //检查参数
        $validator = new \App\Admin\Validator\Department();
        $result = $validator->musts([
            'parent_id', 'name', 'status'
        ])->check($req);

        //根据parent_id找到父部门的的pidStr
        $req['pidstr'] = Services\Department::getPidStr($req['parent_id']);

        $model = Models\Department::create($req);
        if (!$model->save()) {
            return ApiResponse::error(100000, '创建失败');
        }

        $id = $model->id;
        return ApiResponse::success([
            'id' => $id
        ]);
    }

    public function update(Request $request)
    {
        $req = self::paramsFilter($request->post(), [
            'id', ['parentId', 'parent_id'], ['deptName', 'name'], 'status', ['remark', 'note']
        ]);

        //检查参数
        $validator = new \App\Admin\Validator\Department();
        $result = $validator->musts([
            'id', 'status'
        ])->check($req);

        //更新
        DB::beginTransaction();
        try {
            //如果有要更新的部门关系信息
            $flag2UpdatePidstr = false;
            if (isset($req['parent_id'])) {
                if ($req['parent_id'] == $req['id']) {
                    return ApiResponse::error(100000, '不能关联上级部门为当前操作的部门');
                }

                $oldPidstr = Models\Department::where('id', '=', $req['id'])->lock()->value('pidstr');

                $newPidStr = $req['pidstr'] = Services\Department::getPidStr($req['parent_id']);

                if (strpos($newPidStr, '_' . $req['id'] . '_') > 0) {
                    return ApiResponse::error(100000, '不能关联到自己的子部门上');
                }

                $flag2UpdatePidstr = true;
            }

            //上面更新完成就更新
            $result = Models\Department::where([
                ['id', '=', $req['id']]
            ])->update($req);

            if ($result == 0) {
                DB::commit();
                return ApiResponse::error(100000, '找不到对应的对象');
            }

            //如果上面更新成功了
            if ($flag2UpdatePidstr) {
                //暂时没能力在前端处理重复
                if ($oldPidstr != $newPidStr) {
                    //要将所有子部门的关系信息都更新一遍
                    Models\Department::where('pidstr', 'like', $newPidStr . '%')->update([
                        'pidstr' => DB::raw("replace(`pidstr`,'{$oldPidstr}', '{$newPidStr}')")
                    ]);

                    //将所有对应部门数据的用户的部门描述都要换了
                    // Models\Account::where('department','like',$newPidStr.'%')->update([
                    //     'department' => DB::raw("replace(`department`,'{$oldPidstr}', '{$newPidStr}')")
                    // ]);
                }
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            return ApiResponse::error(100000, '更新失败');
        }

        return ApiResponse::success(null);
    }

    public function detail(Request $request)
    {
        $req = [
            'id' => $request->get('id'),
        ];

        $validator = new \App\Admin\Validator\Department();
        $result = $validator->check($req);

        $info = Models\Department::find($req['id'], [
            'id', 'name as deptName', 'parent_id as parentId', 'status', 'note as remark'
        ]);

        if (empty($info)) {
            return ApiResponse::error(100000, '找不到对应的对象');
        }

        return ApiResponse::success($info);
    }

    public function delete(Request $request)
    {
        $req = [
            'id' => $request->get('id')
        ];

        $validator = new \App\Admin\Validator\Department();
        $result = $validator->check($req);

        //不能删除一个还有子部门的部门
        $count = Models\Department::where([
            ['parent_id', '=', $req['id']]
        ])->count('id');

        if ($count > 0) {
            return ApiResponse::error(100000, '不能删除一个还有子部门的部门');
        }

        //不能删除一个还有用户引用的部门
        $count = Models\Account::where([
            ['department', 'like', '%_' . $req['id'] . '_%']
        ])->count('id');

        if ($count > 0) {
            return ApiResponse::error(100000, '不能删除一个还有成员存在的部门');
        }

        $result = Models\Department::where([
            ['id', '=', $req['id']]
        ])->delete();

        if ($result == 0) {
            return ApiResponse::error(100000, '找不到对应的对象');
        }

        return ApiResponse::success($req['id']);
    }
}
